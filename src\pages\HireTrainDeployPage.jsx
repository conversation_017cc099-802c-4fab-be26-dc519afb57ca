import { useEffect, useState, useRef } from "react";
import { Link } from "react-router-dom";
import { Container, Row, Col, Image, ListGroup } from "react-bootstrap";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import "animate.css";
import Hire5 from "../assets/Hire5.png";
import Hire2 from "../assets/Hire2.png";
import Hire3 from "../assets/Hire3.png";
import Hire7 from "../assets/Hire7.png";

gsap.registerPlugin(ScrollTrigger);

const hireTrainDeployData = {
  hero: {
    title: "Hire Train Deploy Services",
    subtitle:
      "Hire Train Deploy (HTD) is a comprehensive workforce solution that combines recruitment, training, and deployment services. We help organizations build skilled teams by hiring the right talent, providing targeted training, and deploying them effectively in their roles.",
    backgroundImage:
      "https://images.unsplash.com/photo-1552664730-d307ca884978?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1926&q=80",
  },
  sections: [
    {
      id: "what-is-htd",
      title: "What is Hire Train Deploy?",
      texts: [
        "Hire Train Deploy (HTD) is an innovative workforce solution that addresses the skills gap in the industry by combining three essential phases: strategic hiring, comprehensive training, and effective deployment.",
        "This model ensures that organizations get job-ready professionals who are specifically trained for their requirements, reducing onboarding time and increasing productivity from day one.",
      ],
      image:
         Hire7,
      alt: "Hire Train Deploy process overview",
      reversed: false,
    },
    {
      id: "three-phases",
      title: "The Three Phases of HTD",
      texts: [
        "Our HTD model follows a systematic approach that ensures maximum success for both candidates and organizations.",
      ],
      listItems: [
        "HIRE: We identify and recruit candidates with the right aptitude and potential, focusing on their ability to learn and adapt rather than just existing skills.",
        "TRAIN: Comprehensive training programs designed specifically for your industry and role requirements, delivered by expert trainers and industry professionals.",
        "DEPLOY: Strategic placement of trained candidates in roles where they can immediately contribute and grow, with ongoing support during the transition period.",
      ],
      image:
        Hire2,
      alt: "Three phases of Hire Train Deploy",
      reversed: true,
    },
    {
      id: "benefits",
      title: "Benefits of HTD Services",
      texts: [""],
      listItems: [
        "Reduced Time-to-Productivity: Candidates are job-ready from day one, significantly reducing the typical ramp-up time.",
        "Cost-Effective Solution: Lower overall hiring and training costs compared to traditional recruitment and internal training programs.",
        "Customized Training: Training programs tailored specifically to your organization's needs, tools, and processes.",
        "Quality Assurance: Rigorous selection and training processes ensure high-quality candidates who meet your standards.",
        // "Scalable Solution: Easily scale your workforce up or down based on project requirements and business needs.",
        // "Risk Mitigation: Reduced hiring risks as candidates are pre-trained and assessed for role suitability.",
        // "**Industry Expertise:** Access to trainers and professionals with deep industry knowledge and experience.",
        // "**Ongoing Support:** Continuous support during the deployment phase to ensure successful integration."
      ],
      image:
       Hire5,
      alt: "Benefits of Hire Train Deploy services",
      reversed: false,
    },
    {
      id: "industries",
      title: "Industries We Serve",
      texts: [""],
      listItems: [
        "Information Technology: Software development, cybersecurity, data analytics, cloud computing, and emerging technologies.",
        "Healthcare: Medical coding, healthcare administration, clinical research, and healthcare IT systems.",
        "Financial Services: Banking operations, insurance processing, financial analysis, and fintech solutions.",
        "Manufacturing: Quality control, process optimization, supply chain management, and industrial automation.",
        // "Telecommunications: Network management, customer service, technical support, and telecommunications infrastructure.",
        // "**Retail & E-commerce:** Digital marketing, customer experience, inventory management, and e-commerce platforms."
      ],
      image:
       Hire3,
      alt: "Industries served by HTD services",
      reversed: true,
    },
  ],
};

const HireTrainDeployPage = () => {
  const heroRef = useRef(null);
  const sectionsRef = useRef([]);

  useEffect(() => {
    window.scrollTo(0, 0);

    // GSAP Animations
    const ctx = gsap.context(() => {
      // Hero section animation
      gsap.from(heroRef.current.querySelector("h1"), {
        y: 100,
        opacity: 0,
        duration: 1.2,
        ease: "power3.out",
      });

      gsap.from(heroRef.current.querySelector("p"), {
        y: 50,
        opacity: 0,
        duration: 1,
        ease: "power2.out",
        delay: 0.3,
      });

      gsap.from(heroRef.current.querySelector(".btn"), {
        y: 30,
        opacity: 0,
        duration: 0.8,
        ease: "back.out(1.7)",
        delay: 0.6,
      });

      // Sections animations
      sectionsRef.current.forEach((section) => {
        if (section) {
          gsap.from(section.querySelector("h2"), {
            y: 50,
            opacity: 0,
            duration: 1,
            ease: "power2.out",
            scrollTrigger: {
              trigger: section,
              start: "top 80%",
              end: "bottom 20%",
              toggleActions: "play none none reverse",
            },
          });

          gsap.from(section.querySelectorAll("p"), {
            y: 30,
            opacity: 0,
            duration: 0.8,
            stagger: 0.2,
            ease: "power2.out",
            scrollTrigger: {
              trigger: section,
              start: "top 75%",
              end: "bottom 20%",
              toggleActions: "play none none reverse",
            },
          });

          gsap.from(section.querySelector("img"), {
            scale: 0.8,
            opacity: 0,
            duration: 1,
            ease: "power2.out",
            scrollTrigger: {
              trigger: section,
              start: "top 70%",
              end: "bottom 20%",
              toggleActions: "play none none reverse",
            },
          });
        }
      });
    }, heroRef);

    return () => ctx.revert();
  }, []);

  const [hoveredImage, setHoveredImage] = useState(null);

  // Reusable Style Objects
  const primaryColor = "#007bff";
  const primaryDarkColor = "#0056b3";
  const primaryRgb = "0,123,255";

  const ctaButtonBaseStyle = {
    padding: "1.2rem 3rem",
    fontSize: "1.2rem",
    background: `linear-gradient(95deg, ${primaryColor}, ${primaryDarkColor})`,
    border: "none",
    borderRadius: "50px",
    boxShadow: `0 8px 25px rgba(${primaryRgb}, 0.3)`,
    transition: "all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1)",
    transform: "translateY(0)",
    color: "#fff",
    textDecoration: "none",
    display: "inline-block",
  };

  const featureImageContainerStyle = (isHovered) => ({
    borderRadius: "1.25rem",
    overflow: "hidden",
    boxShadow: isHovered
      ? "0 1.25rem 3.5rem rgba(0,0,0,0.2)"
      : "0 0.75rem 2rem rgba(0,0,0,0.1)",
    transition: "transform 0.4s ease-out, box-shadow 0.4s ease-out",
    transform: isHovered
      ? "scale(1.04) translateY(-8px)"
      : "scale(1) translateY(0)",
    backgroundColor: "#f0f2f5",
  });

  const featureImageStyle = {
    width: "100%",
    height: "100%",
    minHeight: "400px",
    objectFit: "cover",
    transition: "transform 0.6s ease",
  };

  return (
    <div
      className="hire-train-deploy-page-wrapper min-h-screen relative overflow-hidden"
      style={{
        background: "linear-gradient(135deg, rgba(0, 41, 86, 0.95) 0%, rgba(0, 41, 86, 0.98) 100%)",
        backdropFilter: "blur(10px)",
      }}
    >
      {/* Background Grid Pattern */}
      <div className="absolute inset-0 z-0">
        <div
          className="absolute inset-0 opacity-20"
          style={{
            backgroundImage: `linear-gradient(rgba(0, 160, 233, 0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(0, 160, 233, 0.1) 1px, transparent 1px)`,
            backgroundSize: "50px 50px",
          }}
        />
        {/* Animated Background Icons */}
        {[
          // {
          //   icon: "fa-graduation-cap",
          //   top: "10%",
          //   left: "5%",
          //   delay: 0,
          // },
          // {
          //   icon: "fa-rocket",
          //   top: "20%",
          //   right: "8%",
          //   delay: 1,
          // },
          // {
          //   icon: "fa-users",
          //   bottom: "15%",
          //   left: "3%",
          //   delay: 2,
          // },
          // {
          //   icon: "fa-cogs",
          //   bottom: "25%",
          //   right: "10%",
          //   delay: 3,
          // },
        ].map((item, index) => (
          <div
            key={index}
            className="position-absolute"
            style={{
              top: item.top,
              left: item.left,
              right: item.right,
              bottom: item.bottom,
              animation: `float 6s ease-in-out infinite`,
              animationDelay: `${item.delay}s`,
              opacity: 0.1,
            }}
          >
            <i
              className={`fas ${item.icon} text-white`}
              style={{ fontSize: "2rem" }}
            ></i>
          </div>
        ))}
      </div>
      <div className="relative z-10">
      {/* Hero Section */}
      <section
        ref={heroRef}
        className="hire-train-deploy-hero-section text-white text-center d-flex align-items-center position-relative overflow-hidden"
        style={{
          backgroundImage: `linear-gradient(rgba(0, 41, 86, 0.85), rgba(0, 41, 86, 0.95)), url(${hireTrainDeployData.hero.backgroundImage})`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          minHeight: "100vh",
          padding: "8rem 0",
        }}
      >
        {/* Animated Background Elements */}
        <div className="position-absolute w-100 h-100" style={{ zIndex: 1 }}>
          {/* Floating HTD Icons */}
          {[
            // { icon: "fa-user-plus", top: "15%", left: "10%", delay: 0 },
            // { icon: "fa-graduation-cap", top: "25%", right: "15%", delay: 1 },
            // { icon: "fa-rocket", bottom: "20%", left: "8%", delay: 2 },
            // { icon: "fa-cogs", bottom: "30%", right: "12%", delay: 3 },
          ].map((item, index) => (
            <div
              key={index}
              className="position-absolute"
              style={{
                ...item,
                width: "60px",
                height: "60px",
                background: "rgba(0, 160, 233, 0.1)",
                borderRadius: "50%",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                backdropFilter: "blur(10px)",
                border: "1px solid rgba(0, 160, 233, 0.2)",
                animation: `float 6s ease-in-out infinite`,
                animationDelay: `${item.delay}s`,
              }}
            >
              <i
                className={`fas ${item.icon}`}
                style={{
                  color: "rgba(0, 160, 233, 0.8)",
                  fontSize: "1.5rem",
                }}
              />
            </div>
          ))}

          {/* Gradient Orbs */}
          <div
            className="position-absolute"
            style={{
              width: "300px",
              height: "300px",
              background:
                "radial-gradient(circle, rgba(0, 160, 233, 0.15) 0%, transparent 70%)",
              borderRadius: "50%",
              top: "20%",
              right: "10%",
              filter: "blur(40px)",
              animation: "pulse 4s ease-in-out infinite",
            }}
          />
        </div>
        <Container className="position-relative z-index-1">
          <h1
            style={{
              fontSize: "clamp(1.875rem, 5vw, 4rem)",
              fontWeight: "800",
              letterSpacing: "2.6px",
              marginBottom: "1rem",
              background: "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              backgroundClip: "text",
              textShadow: "0 0 30px rgba(0, 160, 233, 0.3)",
            }}
          >
            {hireTrainDeployData.hero.title}
          </h1>
          <p
            className="text-white/85 mb-5 mx-auto animate__animated animate__fadeInUp animate__slow"
            style={{
              maxWidth: "1200px",
              textShadow: "1px 1px 3px rgba(0,0,0,0.4)",
              fontSize: "clamp(1rem, 2.5vw, 1.45rem)",
            }}
          >
            {hireTrainDeployData.hero.subtitle}
          </p>
        </Container>
      </section>

      {/* Feature Sections */}
      <div
        className="py-5 py-md-6"
        style={{
          background:
            "linear-gradient(135deg, rgba(0, 41, 86, 0.8) 0%, rgba(0, 41, 86, 0.9) 100%)",
          backdropFilter: "blur(10px)",
        }}
      >
        <Container>
          {hireTrainDeployData.sections.map((section, idx) => (
            <section
              key={section.id}
              ref={(el) => (sectionsRef.current[idx] = el)}
              className="mb-5 mb-md-6 py-3"
            >
              {/* Centered Heading Above Content */}
              <Row >
                <Col xs={12}>
                  <h2
                    className="fw-bold text-center"
                    style={{
                      fontSize: "clamp(1.5rem, 4vw, 2.25rem)",
                      fontWeight: "800",
                      letterSpacing: "2.6px",
                      marginBottom: "2rem",
                      background:
                        "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
                      WebkitBackgroundClip: "text",
                      WebkitTextFillColor: "transparent",
                      backgroundClip: "text",
                      textShadow: "0 0 30px rgba(0, 160, 233, 0.3)",
                    }}
                  >
                    {section.title}
                  </h2>
                </Col>
              </Row>

              {/* Enhanced Accent Line */}
          <div className="w-30 h-1 mx-auto relative mb-5">
            <div
              className="w-full h-full rounded-sm shadow-glow"
              style={{
                background: 'linear-gradient(90deg, transparent, #00a0e9, transparent)',
              }}
            />
          </div>

              {/* Content and Image Row */}
              <Row
                className={`align-items-center g-4 g-lg-5 ${
                  section.reversed ? "flex-row-reverse" : ""
                }`}
              >
                <Col
                  lg={6}
                  md={12}
                  className={`${section.reversed ? "ps-lg-4" : "pe-lg-4"}`}
                >
                  <div className="content-wrapper">
                    {section.texts.map((text, textIdx) => (
                      <p
                        key={textIdx}
                        className="mb-3 mb-md-4"
                        style={{
                          fontSize: "1.1rem",
                          lineHeight: "1.7",
                          color: "rgba(255, 255, 255, 0.9)",
                          textAlign: "justify",
                        }}
                      >
                        {text}
                      </p>
                    ))}
                    {section.listItems && (
                      <div className="mt-3 mt-md-4 mb-3 mb-md-4">
                        {section.listItems.map((item, itemIdx) => (
                          <div
                            key={itemIdx}
                            className="d-flex align-items-start mb-3"
                            style={{
                              fontSize: "1.2rem",
                              lineHeight: "1.6",
                              color: "rgba(255, 255, 255, 0.85)",
                            }}
                          >
                            <i
                              className="fas fa-check-circle me-3 flex-shrink-0 mt-1"
                              style={{
                                fontSize: "1.3rem",
                                color: "#00a0e9",
                              }}
                            ></i>
                            <span style={{ textAlign: "justify" }}>{item}</span>
                          </div>
                        ))}
                      </div>
                    )}
                   
                  </div>
                </Col>
                <Col lg={6} md={12} className="d-flex justify-content-center">
                  <div
                    className="image-container"
                    style={{
                      ...featureImageContainerStyle(
                        hoveredImage === section.id
                      ),
                      maxWidth: "100%",
                      width: "100%",
                    }}
                    onMouseEnter={() => setHoveredImage(section.id)}
                    onMouseLeave={() => setHoveredImage(null)}
                  >
                    <Image
                      src={section.image}
                      alt={section.alt}
                      fluid
                      className="animate__animated animate__fadeInRight animate__delay-0.5s"
                      style={{
                        ...featureImageStyle,
                        transform:
                          hoveredImage === section.id
                            ? "scale(1.05)"
                            : "scale(1)",
                        height: "350px",
                        objectFit: "cover",
                      }}
                    />
                  </div>
                </Col>
              </Row>
            </section>
          ))}
        </Container>
      </div>

      {/* Global CSS */}
      <style>{`
                :root {
                    --bs-primary: ${primaryColor};
                    --bs-primary-dark: ${primaryDarkColor};
                    --bs-primary-rgb: ${primaryRgb};
                }

                h1, h2, h3, h4, h5, h6 {
                    line-height: 1.2;
                }

                p {
                    line-height: 1.75;
                }

                .container {
                    padding-left: 1.5rem;
                    padding-right: 1.5rem;
                }

                .py-5 { padding-top: 4rem !important; padding-bottom: 4rem !important; }
                .py-6 { padding-top: 6rem !important; padding-bottom: 6rem !important; }
                .py-7 { padding-top: 8rem !important; padding-bottom: 8rem !important; }

                .content-wrapper {
                    padding: 0;
                }

                .image-container {
                    position: relative;
                    overflow: hidden;
                }

                @keyframes float {
                    0%, 100% {
                        transform: translateY(0px) rotate(0deg);
                        opacity: 0.6;
                    }
                    33% {
                        transform: translateY(-15px) rotate(120deg);
                        opacity: 1;
                    }
                    66% {
                        transform: translateY(5px) rotate(240deg);
                        opacity: 0.8;
                    }
                }

                @keyframes pulse {
                    0%, 100% {
                        transform: scale(1);
                        opacity: 0.15;
                    }
                    50% {
                        transform: scale(1.1);
                        opacity: 0.25;
                    }
                }

                @media (min-width: 768px) {
                    .py-md-5 { padding-top: 4rem !important; padding-bottom: 4rem !important; }
                    .py-md-6 { padding-top: 6rem !important; padding-bottom: 6rem !important; }
                    .py-md-7 { padding-top: 8rem !important; padding-bottom: 8rem !important; }
                    .mb-md-5 { margin-bottom: 4rem !important; }
                    .mb-md-6 { margin-bottom: 6rem !important; }
                    .mb-md-8 { margin-bottom: 8rem !important; }
                }

                @keyframes float {
                    0%, 100% {
                        transform: translateY(0px);
                    }
                    50% {
                        transform: translateY(-20px);
                    }
                }
            `}</style>
      </div>
    </div>
  );
};

export default HireTrainDeployPage;
