import React, { useState, useEffect, useRef } from "react";
// Assuming gsap is imported and configured in your project
import { gsap } from "gsap"; 

// Mock useCookieConsent for demonstration. Replace with your actual implementation.
const useCookieConsent = () => {
  const [consent, setConsent] = useState(true); // Assume consent is given by default for demo

  useEffect(() => {
    // In a real app, you'd check a cookie or local storage here
    const userConsent = localStorage.getItem("cookieConsent");
    if (userConsent === "granted") {
      setConsent(true);
    } else {
      setConsent(false); // Or prompt user for consent
    }
  }, []);

  const hasConsent = () => consent;
  const grantConsent = () => {
    setConsent(true);
    localStorage.setItem("cookieConsent", "granted");
  };
  const revokeConsent = () => {
    setConsent(false);
    localStorage.removeItem("cookieConsent");
  };

  return { hasConsent, grantConsent, revokeConsent };
};


// Assuming chatStyles is defined elsewhere, e.g., in a separate CSS file or a string constant
// For this example, I'm defining a minimal chatStyles just to make the code runnable,
// but your actual chatStyles should contain all the specific styling you have.
const chatStyles = `
  .makonis-popup {
    position: fixed;
    bottom: 90px;
    right: 20px;
    width: 380px;
    height: 520px;
    background: #fff;
    border-radius: 24px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    z-index: 9998;
    transform-origin: bottom right;
  }
  @media (max-width: 480px) {
    .makonis-popup {
      bottom: 0;
      right: 0;
      width: 100vw;
      height: 100vh;
      border-radius: 0;
    }
  }

  .makonis-chat-icon {
    background: linear-gradient(135deg, #002956 0%, #00a0e9 100%);
    box-shadow: 0 4px 15px rgba(0, 160, 233, 0.6);
    transition: all 0.3s ease;
  }
  .makonis-chat-icon:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(0, 160, 233, 0.8);
  }

  .makonis-welcome-message {
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(8px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }

  /* Form specific styles */
  .makonis-form-header {
    text-align: center;
    margin-bottom: 25px;
  }
  .makonis-form-title {
    font-size: 24px;
    color: #002956;
    margin-bottom: 8px;
    font-weight: 700;
  }
  .makonis-form-subtitle {
    font-size: 15px;
    color: #6c757d;
    line-height: 1.6;
    margin-bottom: 0;
  }

  .makonis-input-group {
    /* No position: relative needed if label is always outside */
    margin-bottom: 25px;
    display: flex;
    flex-direction: column; /* Ensures label and input stack vertically */
  }

  /* --- MODIFIED LABEL STYLE --- */
  .makonis-input-label {
    position: static; /* Remove absolute positioning */
    top: auto;        /* Remove top override */
    left: auto;       /* Remove left override */
    background: none; /* No background needed as it's not floating over input */
    padding: 0;       /* No padding needed */
    z-index: auto;    /* Remove z-index override */

    font-size: 14px; /* Slightly larger for clarity as a separate heading */
    color: #495057; /* A bit darker for prominence */
    font-weight: 600; /* More prominent */
    margin-bottom: 8px; /* Space between label and input */
    pointer-events: auto; /* Allow clicks on label */
    transition: none; /* No transition for floating effect */
    align-self: flex-start; /* Aligns the label to the start of the column */
  }
  .makonis-input-label i {
    margin-right: 6px; /* Slightly more space for icon */
    color: #00a0e9;
  }

  .makonis-inp,
  .makonis-select {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ced4da;
    border-radius: 8px;
    font-size: 15px;
    color: #495057;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
    background-color: #f8f9fa; /* Light background for inputs */
    outline: none;
  }
  .makonis-inp:focus,
  .makonis-select:focus {
    border-color: #00a0e9;
    box-shadow: 0 0 0 0.25rem rgba(0, 160, 233, 0.25);
    background-color: #fff;
  }
  .makonis-inp::placeholder {
    color: #adb5bd;
  }
  .makonis-select {
    appearance: none; /* Remove default arrow */
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2300a0e9'%3E%3Cpath d='M7 10l5 5 5-5z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 16px;
  }

  .makonis-phone-container {
    display: flex;
    width: 100%;
    border: 1px solid #ced4da;
    border-radius: 8px;
    overflow: hidden;
    background: #f8f9fa;
    transition: border 0.3s, box-shadow 0.3s;
  }
  .makonis-phone-container:focus-within {
    border: 2px solid #00a0e9;
    box-shadow: 0 0 4px rgba(0, 160, 233, 0.4);
  }

  .makonis-select-prefix {
    flex: 0 0 90px;
    padding: 10px 6px;
    border: none;
    background: #f8f9fa;
    font-weight: 600;
    font-size: 14px;
    color: #1f2937;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    outline: none;
  }

  .makonis-checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 12px; /* Space between checkboxes */
    padding: 10px 5px;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    background-color: #f8f9fa;
  }
  .makonis-checkbox-item {
    display: flex;
    align-items: center;
    cursor: pointer;
  }
  .makonis-checkbox {
    width: 18px;
    height: 18px;
    margin-right: 8px;
    accent-color: #00a0e9; /* Checkbox color */
    cursor: pointer;
  }
  .makonis-checkbox-label {
    font-size: 15px;
    color: #495057;
    cursor: pointer;
    font-weight: 400; /* Less bold than input labels */
    margin-bottom: 0;
  }

  .makonis-submit-btn {
    width: 100%;
    padding: 14px 20px;
    background: linear-gradient(135deg, #002956 0%, #00a0e9 100%);
    color: #fff;
    border: none;
    border-radius: 25px;
    font-size: 17px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 160, 233, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 30px;
  }
  .makonis-submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 160, 233, 0.6);
  }
  .makonis-submit-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }

  .makonis-info-notice {
    background-color: #e0f2f7;
    border-left: 4px solid #00a0e9;
    padding: 12px 15px;
    border-radius: 8px;
    font-size: 13px;
    color: #004d66;
    margin-top: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
  }
  .makonis-info-notice i {
    color: #00a0e9;
  }

  /* Chat Bubble Styles */
  .makonis-bubble-scroll {
    position: relative;
    background: url('https://www.transparenttextures.com/patterns/clean-textile.png') repeat; /* Example subtle texture */
  }

  .makonis-message-user {
    background: linear-gradient(90deg, #00a0e9 0%, #002956 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(0, 41, 86, 0.2);
  }

  .makonis-message-bot {
    background: #e9f5fd;
    color: #002956;
    box-shadow: 0 2px 8px rgba(0, 160, 233, 0.08);
  }

  .makonis-typing-indicator {
    background: #e9f5fd;
    box-shadow: 0 2px 8px rgba(0, 160, 233, 0.08);
  }

  .makonis-quick-btn {
    background-color: #e9f5fd;
    color: #002956;
    border: 1px solid #00a0e9;
    transition: all 0.2s ease;
  }
  .makonis-quick-btn:hover {
    background-color: #00a0e9;
    color: #fff;
    box-shadow: 0 2px 8px rgba(0, 160, 233, 0.3);
  }

  .makonis-input-container {
    background: #f0f8ff; /* Light background for input area */
    border-top: 1px solid #e0e0e0;
  }
`;


// Injects custom styles once on first mount
function useInjectChatStyles() {
  useEffect(() => {
    if (!document.getElementById('makonis-chat-style')) {
      const style = document.createElement("style");
      style.id = "makonis-chat-style";
      style.innerHTML = chatStyles;
      document.head.appendChild(style);
    }
  }, []);
}

const countryCodes = [
  { name: "United States", code: "+1", flag: "🇺🇸" },
  { name: "Canada", code: "+1", flag: "🇨🇦" },
  { name: "United Kingdom", code: "+44", flag: "🇬🇧" },
  { name: "India", code: "+91", flag: "🇮🇳" },
  { name: "Australia", code: "+61", flag: "🇦🇺" },
  { name: "Germany", code: "+49", flag: "🇩🇪" },
  { name: "France", code: "+33", flag: "🇫🇷" },
  { name: "Japan", code: "+81", flag: "🇯🇵" },
  { name: "China", code: "+86", flag: "🇨🇳" },
  { name: "Brazil", code: "+55", flag: "🇧🇷" },
  // Add more countries as needed
  { name: "Afghanistan", code: "+93", flag: "🇦🇫" },
  { name: "Albania", code: "+355", flag: "🇦🇱" },
  { name: "Algeria", code: "+213", flag: "🇩🇿" },
  { name: "Andorra", code: "+376", flag: "🇦🇩" },
  { name: "Angola", code: "+244", flag: "🇦🇴" },
  { name: "Argentina", code: "+54", flag: "🇦🇷" },
  { name: "Austria", code: "+43", flag: "🇦🇹" },
  { name: "Belgium", code: "+32", flag: "🇧🇪" },
  { name: "Bangladesh", code: "+880", flag: "🇧🇩" },
  { name: "Bhutan", code: "+975", flag: "🇧🇹" },
  { name: "Bolivia", code: "+591", flag: "🇧🇴" },
  { name: "Botswana", code: "+267", flag: "🇧🇼" },
  { name: "Bulgaria", code: "+359", flag: "🇧🇬" },
  { name: "Cambodia", code: "+855", flag: "🇰🇭" },
  { name: "Cameroon", code: "+237", flag: "🇨🇲" },
  { name: "Chile", code: "+56", flag: "🇨🇱" },
  { name: "Colombia", code: "+57", flag: "🇨🇴" },
  { name: "Croatia", code: "+385", flag: "🇭🇷" },
  { name: "Cuba", code: "+53", flag: "🇨🇺" },
  { name: "Cyprus", code: "+357", flag: "🇨🇾" },
  { name: "Czech Republic", code: "+420", flag: "🇨🇿" },
  { name: "Denmark", code: "+45", flag: "🇩🇰" },
  { name: "Dominican Republic", code: "+1-809", flag: "🇩🇴" }, // Example with area code
  { name: "Ecuador", code: "+593", flag: "🇪🇨" },
  { name: "Egypt", code: "+20", flag: "🇪🇬" },
  { name: "Estonia", code: "+372", flag: "🇪🇪" },
  { name: "Ethiopia", code: "+251", flag: "🇪🇹" },
  { name: "Finland", code: "+358", flag: "🇫🇮" },
  { name: "Greece", code: "+30", flag: "🇬🇷" },
  { name: "Hong Kong", code: "+852", flag: "🇭🇰" },
  { name: "Hungary", code: "+36", flag: "🇭🇺" },
  { name: "Iceland", code: "+354", flag: "🇮🇸" },
  { name: "Indonesia", code: "+62", flag: "🇮🇩" },
  { name: "Iran", code: "+98", flag: "🇮🇷" },
  { name: "Iraq", code: "+964", flag: "🇮🇶" },
  { name: "Ireland", code: "+353", flag: "🇮🇪" },
  { name: "Israel", code: "+972", flag: "🇮🇱" },
  { name: "Italy", code: "+39", flag: "🇮🇹" },
  { name: "Jamaica", code: "+1-876", flag: "🇯🇲" },
  { name: "Jordan", code: "+962", flag: "🇯🇴" },
  { name: "Kenya", code: "+254", flag: "🇰🇪" },
  { name: "Kuwait", code: "+965", flag: "🇰🇼" },
  { name: "Laos", code: "+856", flag: "🇱🇦" },
  { name: "Latvia", code: "+371", flag: "🇱🇻" },
  { name: "Lebanon", code: "+961", flag: "🇱🇧" },
  { name: "Liberia", code: "+231", flag: "🇱🇷" },
  { name: "Libya", code: "+218", flag: "🇱🇾" },
  { name: "Liechtenstein", code: "+423", flag: "🇱🇮" },
  { name: "Lithuania", code: "+370", flag: "🇱🇹" },
  { name: "Luxembourg", code: "+352", flag: "🇱🇺" },
  { name: "Macau", code: "+853", flag: "🇲🇴" },
  { name: "Madagascar", code: "+261", flag: "🇲🇬" },
  { name: "Malawi", code: "+265", flag: "🇲🇼" },
  { name: "Malaysia", code: "+60", flag: "🇲🇾" },
  { name: "Maldives", code: "+960", flag: "🇲🇻" },
  { name: "Mali", code: "+223", flag: "🇲🇱" },
  { name: "Malta", code: "+356", flag: "🇲🇹" },
  { name: "Mexico", code: "+52", flag: "🇲🇽" },
  { name: "Moldova", code: "+373", flag: "🇲🇩" },
  { name: "Monaco", code: "+377", flag: "🇲🇨" },
  { name: "Mongolia", code: "+976", flag: "🇲🇳" },
  { name: "Montenegro", code: "+382", flag: "🇲🇪" },
  { name: "Morocco", code: "+212", flag: "🇲🇦" },
  { name: "Mozambique", code: "+258", flag: "🇲🇿" },
  { name: "Myanmar (Burma)", code: "+95", flag: "🇲🇲" },
  { name: "Namibia", code: "+264", flag: "🇳🇦" },
  { name: "Nepal", code: "+977", flag: "🇳🇵" },
  { name: "Netherlands", code: "+31", flag: "🇳🇱" },
  { name: "New Zealand", code: "+64", flag: "🇳🇿" },
  { name: "Nicaragua", code: "+505", flag: "🇳🇮" },
  { name: "Niger", code: "+227", flag: "🇳🇪" },
  { name: "Nigeria", code: "+234", flag: "🇳🇬" },
  { name: "North Korea", code: "+850", flag: "🇰🇵" },
  { name: "Norway", code: "+47", flag: "🇳🇴" },
  { name: "Oman", code: "+968", flag: "🇴🇲" },
  { name: "Pakistan", code: "+92", flag: "🇵🇰" },
  { name: "Panama", code: "+507", flag: "🇵🇦" },
  { name: "Papua New Guinea", code: "+675", flag: "🇵🇬" },
  { name: "Paraguay", code: "+595", flag: "🇵🇾" },
  { name: "Peru", code: "+51", flag: "🇵🇪" },
  { name: "Philippines", code: "+63", flag: "🇵🇭" },
  { name: "Poland", code: "+48", flag: "🇵🇱" },
  { name: "Portugal", code: "+351", flag: "🇵🇹" },
  { name: "Qatar", code: "+974", flag: "🇶🇦" },
  { name: "Romania", code: "+40", flag: "🇷🇴" },
  { name: "Russia", code: "+7", flag: "🇷🇺" },
  { name: "Rwanda", code: "+250", flag: "🇷🇼" },
  { name: "San Marino", code: "+378", flag: "🇸🇲" },
  { name: "Saudi Arabia", code: "+966", flag: "🇸🇦" },
  { name: "Senegal", code: "+221", flag: "🇸🇳" },
  { name: "Serbia", code: "+381", flag: "🇷🇸" },
  { name: "Singapore", code: "+65", flag: "🇸🇬" },
  { name: "Slovakia", code: "+421", flag: "🇸🇰" },
  { name: "Slovenia", code: "+386", flag: "🇸🇮" },
  { name: "Somalia", code: "+252", flag: "🇸🇴" },
  { name: "South Africa", code: "+27", flag: "🇿🇦" },
  { name: "South Korea", code: "+82", flag: "🇰🇷" },
  { name: "Spain", code: "+34", flag: "🇪🇸" },
  { name: "Sri Lanka", code: "+94", flag: "🇱🇰" },
  { name: "Sudan", code: "+249", flag: "🇸🇩" },
  { name: "Sweden", code: "+46", flag: "🇸🇪" },
  { name: "Switzerland", code: "+41", flag: "🇨🇭" },
  { name: "Syria", code: "+963", flag: "🇸🇾" },
  { name: "Taiwan", code: "+886", flag: "🇹🇼" },
  { name: "Tanzania", code: "+255", flag: "🇹🇿" },
  { name: "Thailand", code: "+66", flag: "🇹🇭" },
  { name: "Togo", code: "+228", flag: "🇹🇬" },
  { name: "Trinidad and Tobago", code: "+1-868", flag: "🇹🇹" },
  { name: "Tunisia", code: "+216", flag: "🇹🇳" },
  { name: "Turkey", code: "+90", flag: "🇹🇷" },
  { name: "Uganda", code: "+256", flag: "🇺🇬" },
  { name: "Ukraine", code: "+380", flag: "🇺🇦" },
  { name: "United Arab Emirates", code: "+971", flag: "🇦🇪" },
  { name: "Uruguay", code: "+598", flag: "🇺🇾" },
  { name: "Uzbekistan", code: "+998", flag: "🇺🇿" },
  { name: "Venezuela", code: "+58", flag: "🇻🇪" },
  { name: "Vietnam", code: "+84", flag: "🇻🇳" },
  { name: "Yemen", code: "+967", flag: "🇾🇪" },
  { name: "Zambia", code: "+260", flag: "🇿🇲" },
  { name: "Zimbabwe", code: "+263", flag: "🇿🇼" },
];


const ChatBot = () => {
  useInjectChatStyles();

  // Cookie consent integration
  const { hasConsent } = useCookieConsent();

  // State management
  const [isOpen, setIsOpen] = useState(false);
  const [showWelcomeMessage, setShowWelcomeMessage] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [isBotTyping, setIsBotTyping] = useState(false);
  const formNameInput = useRef(null);
  const messageInput = useRef(null);

  const [formData, setFormData] = useState({
    name: "",
    email: "",
    countryCode: "+91", // New state for country code
    mobile: "",
    department: "",
    productsInterested: [],
  });
  const [messages, setMessages] = useState([
    {
      id: 1,
      text:
        "Hi there, I'm an AI Agent from Makonis. If you have any questions just let me know. Saw that you're interested in our products/services. I'm available if you have any questions or need help.",
      sender: "bot",
      timestamp: new Date(),
    },
  ]);
  const [inputMessage, setInputMessage] = useState("");
  const chatPopupRef = useRef(null);
  const chatIconRef = useRef(null);
  const messagesEndRef = useRef(null);
  const welcomeMessageRef = useRef(null);

  // Configuration
  const departments = [
    { value: "products", label: "Products" },
    { value: "job_openings", label: "Job Openings" },
    { value: "others", label: "Others" },
  ];
  const subDepartments = {
    products: ["Makoplus", "Trading Intelligence", "ATS - Talent Track Pro"],
    job_openings: ["Domestic Openings", "US Openings"],
  };

  // Enhanced cookie utility with consent checking
  const setCookie = (name, value, days = 30) => {
    // Only set cookies if user has given consent
    if (!hasConsent()) {
      console.log('Cookie consent not given, skipping cookie storage');
      return;
    }

    const expires = new Date();
    expires.setTime(expires.getTime() + days * 24 * 60 * 60 * 1000);
    document.cookie = `${name}=${encodeURIComponent(
      JSON.stringify(value)
    )};expires=${expires.toUTCString()};path=/`;
  };

  const getCookie = (name) => {
    // Only get cookies if user has given consent
    if (!hasConsent()) {
      return null;
    }

    const nameEQ = name + "=";
    const ca = document.cookie.split(";");
    for (let i = 0; i < ca.length; i++) {
      let c = ca[i];
      while (c.charAt(0) === " ") c = c.substring(1, c.length);
      if (c.indexOf(nameEQ) === 0) {
        try {
          return JSON.parse(decodeURIComponent(c.substring(nameEQ.length)));
        } catch {
          return null;
        }
      }
    }
    return null;
  };

  // Effects: On load, inject saved user fields (only if consent given)
  useEffect(() => {
    if (hasConsent()) {
      const savedUserData = getCookie("makonisUserData");
      if (savedUserData) {
        setFormData((f) => ({ ...f, ...savedUserData }));
      }
    }
  }, [hasConsent]);

  // Debug effect for development
  useEffect(() => {
    console.log('ChatBot state:', { isOpen, showForm, hasConsent: hasConsent() });
  }, [isOpen, showForm, hasConsent]);

  // Animate open
  useEffect(() => {
    if (isOpen && chatPopupRef.current) {
      gsap.fromTo(
        chatPopupRef.current,
        { opacity: 0, scale: 0.92, y: 14 },
        { opacity: 1, scale: 1, y: 0, duration: 0.26, ease: "back.out(1.6)" }
      );
      setTimeout(() => {
        if (!showForm && messageInput.current) messageInput.current.focus();
      }, 200);
    }
    // When closing, hide form as well
    if (!isOpen) setShowForm(false);
  }, [isOpen]);

  // Animate chat icon
  useEffect(() => {
    if (chatIconRef.current) {
      gsap.to(chatIconRef.current, {
        scale: 1.1,
        duration: 2,
        repeat: -1,
        yoyo: true,
        ease: "power2.inOut",
      });
    }
  }, []);

  // Scroll to bottom on messages update
  useEffect(() => {
    if (messagesEndRef.current)
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
  }, [messages, isBotTyping]);

  // Welcome message on first visit (only if consent given)
  useEffect(() => {
    const hasShownWelcome = sessionStorage.getItem("makonisWelcomeShown");
    if (!hasShownWelcome) {
      const timer = setTimeout(() => {
        setShowWelcomeMessage(true);
        if (welcomeMessageRef.current) {
          gsap.fromTo(
            welcomeMessageRef.current,
            { opacity: 0, scale: 0.9, x: 30 },
            { opacity: 1, scale: 1, x: 0, duration: 0.45, ease: "back.out(1)" }
          );
        }
      }, 1600);
      return () => clearTimeout(timer);
    }
  }, []);
  // Autofocus input on form show
  useEffect(() => {
    if (showForm && formNameInput.current) {
      formNameInput.current.focus();
    }
  }, [showForm]);

  // Event handlers
  const toggleChat = () => {
    if (!isOpen) {
      // Reset or set showForm on opening
      const saved = getCookie && getCookie("makonisUserData");
      if (saved) {
        setFormData({
          ...saved,
          countryCode: saved.countryCode || "+91", // Ensure country code is set from saved data or default
          department: "",
          productsInterested: [],
        });
      }
      setShowForm(true);
      setIsOpen(true);
    } else {
      setShowForm(false);
      setIsOpen(false);
    }
  };

  const closeWelcomeMessage = () => {
    if (welcomeMessageRef.current) {
      gsap.to(welcomeMessageRef.current, {
        opacity: 0,
        scale: 0.8,
        x: 30,
        duration: 0.25,
        onComplete: () => {
          setShowWelcomeMessage(false);
          sessionStorage.setItem("makonisWelcomeShown", "true");
        },
      });
    }
  };

  // Form handling
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    if (type === "checkbox") {
      setFormData((prev) => ({
        ...prev,
        productsInterested: checked
          ? [...prev.productsInterested, value]
          : prev.productsInterested.filter((item) => item !== value),
      }));
    } else {
      setFormData((prev) => ({
        ...prev,
        [name]: value,
      }));
    }
  };

  const handleFormSubmit = (e) => {
    e.preventDefault();
    // Simple validation
    if (!formData.name.trim() || !formData.email.trim() || !formData.mobile.trim()) return;

    // Only save to cookies if consent is given
    if (hasConsent()) {
      setCookie("makonisUserData", {
        name: formData.name,
        email: formData.email,
        countryCode: formData.countryCode, // Save country code
        mobile: formData.mobile,
      });
    }

    setShowForm(false);

    setMessages((prev) => [
      ...prev,
      {
        id: prev.length + 10,
        sender: "user",
        text: `👤 ${formData.name}\n✉️ ${formData.email}\n📱 ${formData.countryCode}-${formData.mobile}${ // Use selected country code
            formData.department
              ? `\nDepartment: ${departments.find((d) => d.value === formData.department)?.label || ""}`
              : ""
          }${formData.productsInterested.length ? `\nInterested In: ${formData.productsInterested.join(", ")}` : ""}`,
        timestamp: new Date(),
      },
    ]);

    setTimeout(() => {
      // Next step: Show dept options or just chat
      if (formData.department === "others") {
        setMessages((prev) => [
          ...prev,
          {
            id: prev.length + 11,
            text: "Thanks! How can I help you today?",
            sender: "bot",
            timestamp: new Date(),
          },
        ]);
      } else {
        const options = subDepartments[formData.department];
        if (options) {
          setMessages((prev) => [
            ...prev,
            {
              id: prev.length + 12,
              text: `Great! Here are the ${
                  formData.department === "products" ? "products" : "job openings"
                } we offer:\n\n${options
                  .map((option, idx) => `${idx + 1}. ${option}`)
                  .join("\n")}\n\nWhich one interests you?`,
              sender: "bot",
              timestamp: new Date(),
            },
          ]);
        }
      }
    }, 600);
  };

  // Chat send functionality
  const handleSendMessage = () => {
    let text = inputMessage.trim();
    if (!text) return;
    const newMessage = {
      id: messages.length + 1,
      text,
      sender: "user",
      timestamp: new Date(),
    };
    setMessages((prev) => [...prev, newMessage]);
    setInputMessage("");

    // Simulate "bot typing" indicator
    setIsBotTyping(true);
    setTimeout(() => {
      setIsBotTyping(false);
      setMessages((prev) => [
        ...prev,
        {
          id: prev.length + 1,
          text: getBotResponse(text),
          sender: "bot",
          timestamp: new Date(),
        },
      ]);
    }, 900 + Math.random() * 900); // random realistic delay
  };

  const handleQuickReply = (text) => {
    setInputMessage(text);
    setTimeout(handleSendMessage, 80);
  };

  const getBotResponse = (userText) => {
    // Customize this for NLP/question handling!
    const l = userText.toLowerCase();
    if (l.includes("price") || l.includes("cost")) return "We offer customized pricing for each client based on requirements. Would you like a call back?";
    if (l.includes("job") || l.includes("career")) return "Our HR team will reach out to you. Any specific job you're looking for?";
    if (l.includes("demo")) return "You can book a product demo. Would you like to schedule one?";
    if (l.includes("hello") || l.includes("hi")) return "Hello! How may I help you today?";
    if (l.includes("thanks") || l.includes("thank you")) return "You're welcome! 😊";
    return "Thank you for your message. We'll get back to you soon.";
  };

  const handleKeyDown = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // JSX
  return (
    <>
      {/* Style Block - Ensures custom styles are applied */}
      <style>{chatStyles}</style>

      {/* Welcome popover (on load) */}
      {showWelcomeMessage && !isOpen && (
        <div
          ref={welcomeMessageRef}
          className="position-fixed makonis-welcome-message"
          style={{
            bottom: "89px",
            right: "20px",
            maxWidth: "320px",
            width: "calc(100% - 40px)",
            borderRadius: "20px",
            zIndex: 9997,
            overflow: "hidden",
            padding: "20px",
          }}
        >
          <div className="d-flex justify-content-between align-items-center mb-3">
            <div className="d-flex align-items-center">
              <div
                style={{
                  width: "40px",
                  height: "40px",
                  borderRadius: "50%",
                  background: "linear-gradient(135deg, #002956 0%, #00a0e9 100%)",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  marginRight: "12px",
                }}
              >
                <i className="fas fa-robot text-white" style={{ fontSize: "18px" }}></i>
              </div>
              <span
                style={{
                  fontWeight: "600",
                  color: "#002956",
                  fontSize: "16px",
                }}
              >
                Makonis AI Assistant
              </span>
            </div>
            <button
              onClick={closeWelcomeMessage}
              style={{
                background: "none",
                border: "none",
                color: "#8ca8c7",
                cursor: "pointer",
                fontSize: "20px",
                lineHeight: "1",
                transition: "all 0.3s ease",
              }}
              aria-label="Close welcome message"
              onMouseEnter={(e) => e.target.style.color = "#002956"}
              onMouseLeave={(e) => e.target.style.color = "#8ca8c7"}
            >
              <i className="fas fa-times"></i>
            </button>
          </div>
          <p style={{
              margin: 0,
              color: "#495057",
              fontSize: "14px",
              fontWeight: 400,
              lineHeight: "1.6"
          }}>
            Hi there! Interested in our products or services? I'm here to answer your questions.
          </p>
          <div
            style={{
              position: "absolute",
              bottom: "-10px",
              right: "22px",
              width: "0",
              height: "0",
              borderLeft: "10px solid transparent",
              borderRight: "10px solid transparent",
              borderTop: "10px solid rgba(255, 255, 255, 0.95)",
              filter: "drop-shadow(0 4px 6px rgba(0,0,0,0.08))",
            }}
          ></div>
        </div>
      )}

      {/* Chat Popup */}
      {isOpen && (
        <div ref={chatPopupRef} className="makonis-popup">
          {/* Chat header */}
          <div style={{ position: "relative", zIndex: 2 }}>
            <div
              className="text-white d-flex align-items-center"
              style={{
                background: "linear-gradient(135deg, #002956 0%, #00a0e9 97%)",
                borderTopLeftRadius: "24px",
                borderTopRightRadius: "24px",
                padding: "18px 20px 12px 18px",
                gap: "14px"
              }}>
              <div style={{
                width: "38px", height: "38px", background: "#fff",
                borderRadius: "50%", display: "flex", alignItems: "center",
                justifyContent: "center", boxShadow: "0 2px 8px rgba(0, 160, 233, 0.3)"
              }}>
                <i className="fas fa-robot" style={{ color: "#00a0e9", fontSize: "20px" }}/>
              </div>
              <div style={{ flex: 1 }}>
                <div style={{ fontSize: "14px", color: "#cbe7fa", fontWeight: "500" }}>Chat with</div>
                <div style={{ fontWeight: "bold", fontSize: "16px" }}>Makonis AI Assistant</div>
              </div>
              <button
                aria-label="Minimize"
                onClick={toggleChat}
                className="makonis-header-btn"
                style={{
                  background: "none",
                  border: "none",
                  fontSize: "22px",
                  color: "#f8fcff",
                  cursor: "pointer",
                  marginLeft: '8px',
                  transition: "all 0.3s ease"
                }}
              >
                <i className="fas fa-chevron-down"></i>
              </button>
            </div>
            {/* REMOVED: Enhanced header wave SVG */}
          </div>

          {/* Enhanced Form UI */}
          {showForm && (
            <div className="p-4 flex-grow-1 makonis-form-container" style={{overflowY: 'auto'}}>
              <div className="makonis-form-header">
                <h3 className="makonis-form-title">Let's Get Started</h3>
                <p className="makonis-form-subtitle">Tell us about yourself to begin the conversation</p>
              </div>

              <form onSubmit={handleFormSubmit}>
                {/* --- Personal Details Section --- */}
                <div className="makonis-form-section">
                  {/* Name Field */}
                  <div className="makonis-input-group">
                    {/* Label moved above input */}
                    <label className="makonis-input-label" htmlFor="name-input">
                      <i className="fas fa-user"></i>
                      Full Name *
                    </label>
                    <input
                      ref={formNameInput}
                      type="text"
                      name="name"
                      placeholder="Enter your full name"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                      className="form-control makonis-inp"
                      id="name-input" /* Added ID for htmlFor */
                    />
                  </div>

                  {/* Email Field */}
                  <div className="makonis-input-group">
                    {/* Label moved above input */}
                    <label className="makonis-input-label" htmlFor="email-input">
                      <i className="fas fa-envelope"></i>
                      Email Address *
                    </label>
                    <input
                      type="email"
                      name="email"
                      placeholder="Enter your email address"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                      className="form-control makonis-inp"
                      id="email-input" /* Added ID for htmlFor */
                    />
                  </div>

                  {/* Mobile Number Field */}
                  <div className="makonis-input-group">
                    {/* Label moved above input */}
                    <label className="makonis-input-label" htmlFor="mobile-input">
                      <i className="fas fa-phone mr-1"></i>
                      Mobile Number *
                    </label>
                    <div className="makonis-phone-container">
                      {/* Country Code Selector with Flags */}
                      <select
                        name="countryCode"
                        value={formData.countryCode}
                        onChange={handleInputChange}
                        className="makonis-select-prefix"
                        aria-label="Select country code"
                      >
                        {countryCodes.map((country) => (
                          <option key={country.code + country.flag} value={country.code}>
                            {country.flag} {country.code}
                          </option>
                        ))}
                      </select>

                      {/* Phone Number Input */}
                      <input
                        type="tel"
                        name="mobile"
                        pattern="[0-9]{5,15}"
                        placeholder="Enter mobile number"
                        value={formData.mobile}
                        onChange={handleInputChange}
                        required
                        className="makonis-inp"
                        id="mobile-input" /* Added ID for htmlFor */
                      />
                    </div>
                  </div>
                </div> {/* End Personal Details Section */}

                {/* --- Area of Interest Section --- */}
                <div className="makonis-form-section">
                  {/* Department Field */}
                  <div className="makonis-input-group">
                    {/* Label moved above select */}
                    <label className="makonis-input-label" htmlFor="department-select">
                      <i className="fas fa-building"></i>
                      Department *
                    </label>
                    <select
                      name="department"
                      value={formData.department}
                      onChange={handleInputChange}
                      required
                      className="form-control makonis-select"
                      id="department-select" /* Added ID for htmlFor */
                    >
                      <option value="">Choose your area of interest</option>
                      {departments.map((dept) => (
                        <option key={dept.value} value={dept.value}>
                          {dept.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Products Selection (conditional) */}
                  {formData.department === "products" && (
                    <div className="makonis-input-group">
                      {/* Label for checkboxes */}
                      <label className="makonis-input-label">
                        <i className="fas fa-box"></i>
                        Products Interested *
                      </label>
                      <div className="makonis-checkbox-group">
                        {subDepartments.products.map((product) => (
                          <div key={product} className="makonis-checkbox-item">
                            <input
                              type="checkbox"
                              className="makonis-checkbox"
                              value={product}
                              checked={formData.productsInterested.includes(product)}
                              onChange={handleInputChange}
                              id={product.replace(/\s+/g, "")}
                            />
                            <label
                              className="makonis-checkbox-label"
                              htmlFor={product.replace(/\s+/g, "")}
                            >
                              {product}
                            </label>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div> {/* End Area of Interest Section */}

                {/* Submit Button */}
                <button
                  type="submit"
                  className="makonis-submit-btn"
                  disabled={!formData.name.trim() || !formData.email.trim() || !formData.mobile.trim() || !formData.department}
                >
                  <i className="fas fa-comments" style={{marginRight: "8px"}}></i>
                  Start Conversation
                </button>

                {/* Cookie Notice */}
                {!hasConsent() && (
                  <div className="makonis-info-notice">
                    <i className="fas fa-info-circle"></i>
                    <span>Your data won't be saved as cookies are not accepted.</span>
                  </div>
                )}
              </form>
            </div>
          )}

          {/* Actual chat messages */}
          {!showForm && (
            <>
              <div
                className="flex-grow-1 p-4 makonis-bubble-scroll"
                style={{
                  overflowY: "auto",
                  background: "transparent",
                  position: "relative"
                }}
              >
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={`mb-3 d-flex ${
                      message.sender === "user"
                        ? "justify-content-end"
                        : "justify-content-start"
                    }`}
                  >
                    <div
                      className={`px-4 py-3 ${
                        message.sender === "user"
                          ? "makonis-message-user"
                          : "makonis-message-bot"
                      }`}
                      style={{
                        maxWidth: "80%",
                        borderRadius:
                          message.sender === "user"
                            ? "20px 18px 6px 18px"
                            : "20px 20px 20px 6px",
                        color: message.sender === "user" ? "#fff" : "#002956",
                        fontSize: "15px",
                        whiteSpace: "pre-line",
                        marginLeft: message.sender !== "user" ? "4px" : 0,
                        marginRight: message.sender === "user" ? "4px" : 0,
                        fontWeight: "500",
                        lineHeight: "1.5"
                      }}
                    >
                      {message.text}
                    </div>
                  </div>
                ))}
                {isBotTyping && (
                  <div className="mb-3 d-flex justify-content-start">
                    <div
                      className="makonis-typing-indicator"
                      style={{
                        borderRadius: "20px 18px 20px 8px",
                        color: "#00a0e9",
                        fontSize: "15px",
                        padding: "12px 20px 12px 18px",
                        fontStyle: "italic",
                        letterSpacing: "0.5px",
                        maxWidth: "65%",
                        display:"flex",alignItems:"center",gap:"10px"
                      }}
                      aria-live="polite"
                    >
                      <span>
                        <i className="fas fa-circle-notch fa-spin" style={{fontSize:'16px',color:"#00a0e9"}}></i>
                      </span>
                      Typing...
                    </div>
                  </div>
                )}
                <div ref={messagesEndRef} />
              </div>

              {/* Quick Reply buttons */}
              <div className="px-4 pb-3 d-flex gap-2 flex-wrap">
                {["Yes", "No", "Help"].map((text, idx) => (
                  <button
                    key={idx}
                    className="makonis-quick-btn btn btn-sm"
                    style={{
                      borderRadius: "16px",
                      fontSize: "14px",
                      padding: "8px 16px",
                      fontWeight: "500"
                    }}
                    onClick={e => { e.preventDefault(); handleQuickReply(text); }}
                    tabIndex={0}
                  >
                    {text}
                  </button>
                ))}
              </div>

              {/* Input/send section */}
              <div className="p-4 makonis-input-container">
                <div className="d-flex align-items-center">
                  <input
                    ref={messageInput}
                    type="text"
                    placeholder="Type your message…"
                    value={inputMessage}
                    onChange={e => setInputMessage(e.target.value)}
                    onKeyDown={handleKeyDown}
                    disabled={isBotTyping}
                    style={{
                      flex: 1,
                      border: "1px solid rgba(0, 160, 233, 0.2)",
                      outline: "none",
                      padding: "12px 16px",
                      fontSize: "15px",
                      background: "rgba(255, 255, 255, 0.9)",
                      borderRadius: "20px",
                      transition: "all 0.3s ease"
                    }}
                    onFocus={(e) => {
                      e.target.style.borderColor = "#00a0e9";
                      e.target.style.boxShadow = "0 0 0 3px rgba(0, 160, 233, 0.1)";
                    }}
                    onBlur={(e) => {
                      e.target.style.borderColor = "rgba(0, 160, 233, 0.2)";
                      e.target.style.boxShadow = "none";
                    }}
                  />
                  <button
                    onClick={handleSendMessage}
                    disabled={!inputMessage.trim() || isBotTyping}
                    style={{
                      background: "none",
                      border: "none",
                      marginLeft: "12px",
                      outline: 'none',
                      cursor: inputMessage.trim() && !isBotTyping ? "pointer" : "not-allowed",
                      transition: "all 0.3s ease"
                    }}
                    aria-label="Send"
                  >
                    <i
                      className="fab fa-telegram-plane"
                      style={{
                        color: inputMessage.trim() && !isBotTyping ? "#00a0e9" : "#8de1fb",
                        fontSize: "2.2rem",
                        opacity: inputMessage.trim() && !isBotTyping ? 1 : 0.5,
                        transition: "all 0.3s ease"
                      }}
                    />
                  </button>
                </div>
              </div>
            </>
          )}
        </div>
      )}

      {/* Chat circle icon */}
      <div
        ref={chatIconRef}
        onClick={toggleChat}
        className="position-fixed d-flex align-items-center justify-content-center makonis-chat-icon"
        style={{
          bottom: "20px",
          right: "20px",
          width: "65px",
          height: "65px",
          borderRadius: "50%",
          cursor: "pointer",
          zIndex: 9999
        }}
        tabIndex={0}
        title={isOpen ? "Close chat" : "Open chat"}
        aria-label={isOpen ? "Close chat" : "Open chat"}
        onKeyDown={e => { if (e.key === " " || e.key === "Enter") { e.preventDefault(); toggleChat(); }}}
      >
        <i
          className={`fas ${isOpen ? "fa-times" : "fa-comments"} text-white`}
          style={{ fontSize: "1.8rem", transition: "all 0.3s ease" }}
        />
      </div>
    </>
  );
};

export default ChatBot;