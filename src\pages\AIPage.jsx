import React, { useEffect, useState, useRef } from "react";
import { Link } from "react-router-dom";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, Accordion } from "react-bootstrap";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger"; // Corrected import syntax
import "animate.css";

gsap.registerPlugin(ScrollTrigger);

// --- ENHANCED DATA FOR THE PAGE ---
const aiPageData = {
  hero: {
    title: "Intelligent Solutions for Tomorrow",
    subtitle:
      "Harness the power of Artificial Intelligence to transform your business operations, automate complex processes, and unlock unprecedented insights from your data.",
    backgroundImage:
      "https://images.unsplash.com/photo-1677442136019-21780ecad995?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
  },
  solutions: {
    title: "Our AI Solutions",
    description:
      "We offer a comprehensive suite of AI services. Explore our capabilities to see how we can drive your business forward.",
    items: [
      {
        eventKey: "ml",
        title: "Machine Learning",
        text: "Our Machine Learning solutions focus on creating systems that learn and adapt from data. We build custom models to automate processes, predict outcomes, and provide actionable insights that give you a competitive edge.",
        services: [
          "Predictive Analytics & Forecasting",
          "Recommendation Engines",
          "Custom Algorithm Development",
          "Data Clustering & Classification",
        ],
        image:
          "https://images.unsplash.com/photo-1633409361619-b7b2524a3c40?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80",
      },
      {
        eventKey: "cv",
        title: "Computer Vision",
        text: "We empower machines to see and interpret the world. Our computer vision services automate tasks that typically require human vision, such as quality control, surveillance, and medical image analysis, with superhuman accuracy and speed.",
        services: [
          "Object Detection & Tracking",
          "Facial Recognition Systems",
          "Image & Video Analysis",
          "Automated Quality Inspection",
        ],
        image:
          "https://images.unsplash.com/photo-1579551065094-13a83a04c219?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80",
      },
      {
        eventKey: "nlp",
        title: "Natural Language Processing",
        text: "Unlock the value of unstructured text data. Our NLP solutions enable machines to understand and process human language, powering everything from intelligent chatbots and virtual assistants to sentiment analysis and document summarization.",
        services: [
          "Sentiment Analysis",
          "Intelligent Chatbots",
          "Text Summarization",
          "Document Processing & Extraction",
        ],
        image:
          "https://images.unsplash.com/photo-1531746790731-6c087fecd65a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1742&q=80",
      },
    ],
  },
  whyChooseUs: {
    title: "Why Partner With Us?",
    description:
      "We deliver more than just code. We deliver real business value through a combination of technical expertise, strategic thinking, and a commitment to your success.",
    items: [
      {
        icon: "fas fa-handshake",
        title: "Collaborative Partnership",
        text: "We work closely with you at every stage, ensuring our AI solutions are perfectly aligned with your business objectives and deliver measurable ROI.",
      },
      {
        icon: "fas fa-cogs",
        title: "Custom-Built Solutions",
        text: "We don't believe in one-size-fits-all. Every model we build is tailored to your unique data, challenges, and goals for maximum impact.",
      },
      {
        icon: "fas fa-shield-alt",
        title: "Ethical & Secure AI",
        text: "We are committed to building responsible AI. Our practices prioritize fairness, transparency, and robust data security in every project.",
      },
    ],
  },
  process: {
    title: "Our Development Process",
    description:
      "We follow a structured, agile methodology to ensure project success and deliver measurable results.",
    steps: [
      {
        icon: "fas fa-search",
        title: "Discovery & Consultation",
        text: "We start by understanding your goals and challenges to define a clear AI strategy and project roadmap.",
      },
      {
        icon: "fas fa-database",
        title: "Data Strategy & Preparation",
        text: "Our team collects, cleans, and processes your data to create high-quality datasets ready for model training.",
      },
      {
        icon: "fas fa-brain",
        title: "Model Development",
        text: "Using the prepared data, we build and train custom AI models tailored to your specific requirements.",
      },
      {
        icon: "fas fa-rocket",
        title: "Deployment & Integration",
        text: "We seamlessly deploy the AI model into your existing systems and workflows with minimal disruption.",
      },
      {
        icon: "fas fa-chart-line",
        title: "Monitoring & Optimization",
        text: "Post-deployment, we continuously monitor performance and retrain the model to ensure long-term accuracy and value.",
      },
    ],
  },
  industries: {
    title: "Industries We Serve",
    description:
      "Our AI solutions are versatile and can be adapted to drive innovation across a wide range of industries.",
    items: [
      {
        icon: "fas fa-heartbeat",
        name: "Healthcare",
        description:
          "From predictive diagnostics to personalized treatment plans, we help healthcare providers improve patient outcomes and operational efficiency.",
      },
      {
        icon: "fas fa-dollar-sign",
        name: "Finance & Banking",
        description:
          "Enhance security with fraud detection, automate loan processing, and provide personalized financial advice with our AI-powered solutions.",
      },
      {
        icon: "fas fa-shopping-cart",
        name: "E-commerce & Retail",
        description:
          "Optimize supply chains, personalize customer experiences with recommendation engines, and analyze market trends to stay ahead.",
      },
      {
        icon: "fas fa-industry",
        name: "Manufacturing",
        description:
          "Implement predictive maintenance to reduce downtime, automate quality control, and optimize production lines for maximum output.",
      },
    ],
  },
  faq: {
    title: "Frequently Asked Questions",
    items: [
      {
        question: "What kind of data do I need for an AI project?",
        answer:
          "The data required depends on the problem you're trying to solve. Typically, you need a substantial amount of high-quality, relevant data. We can help you with data strategy and acquisition if you don't have the necessary data on hand.",
      },
      {
        question: "How long does a typical AI project take?",
        answer:
          "Project timelines vary based on complexity, data availability, and the scope of work. A pilot project can take a few weeks, while a full-scale enterprise solution may take several months. We provide a detailed timeline after the initial discovery phase.",
      },
      {
        question: "How do you ensure the AI model is accurate?",
        answer:
          "We use rigorous testing and validation techniques on historical data. After deployment, we continuously monitor the model's performance against live data and retrain it as needed to maintain high accuracy and adapt to new patterns.",
      },
      {
        question: "What kind of post-deployment support do you offer?",
        answer:
          "Our partnership doesn't end at deployment. We offer comprehensive support packages that include performance monitoring, regular model retraining, and on-call assistance to ensure your AI solution continues to deliver peak value.",
      },
    ],
  },
  // Removed cta section
};

// --- THE ENHANCED REACT COMPONENT ---
const AIPage = () => {
  const [activeCard, setActiveCard] = useState(0);
  const [isVisible, setIsVisible] = useState({});
  const [stats, setStats] = useState({
    projects: 0,
    accuracy: 0,
    clients: 0,
    models: 0,
  });
  const [neuralNodes, setNeuralNodes] = useState([]);
  const heroRef = useRef(null);
  const statsRef = useRef(null);
  const processRef = useRef(null);
  const networkRef = useRef(null);
  const pageWrapperRef = useRef(null);

  // Initialize and animate neural network
  useEffect(() => {
    const nodes = Array.from({ length: 30 }, (_, i) => ({
      id: i,
      x: Math.random() * 100,
      y: Math.random() * 100,
      size: Math.random() * 3 + 1,
      speed: (Math.random() - 0.5) * 0.2,
      connections: [],
    }));

    nodes.forEach((node, i) => {
      nodes.forEach((otherNode, j) => {
        if (i !== j) {
          const distance = Math.sqrt(
            Math.pow(node.x - otherNode.x, 2) +
              Math.pow(node.y - otherNode.y, 2)
          );
          if (distance < 20 && node.connections.length < 3) {
            node.connections.push(j);
          }
        }
      });
    });
    setNeuralNodes(nodes);

    let animationFrameId;
    const animate = () => {
      setNeuralNodes((prev) =>
        prev.map((node) => ({
          ...node,
          x: (node.x + node.speed + 100) % 100,
          y:
            (node.y + Math.sin(Date.now() * 0.0005 + node.id) * 0.1 + 100) %
            100,
        }))
      );
      animationFrameId = requestAnimationFrame(animate);
    };
    animate();

    return () => cancelAnimationFrame(animationFrameId);
  }, []);

  // GSAP Animations
  useEffect(() => {
    // Scroll to top is now handled globally by useScrollToTop hook

    const ctx = gsap.context(() => {
      // Hero section animations
      gsap.from(heroRef.current.querySelector("h1"), {
        y: 100,
        opacity: 0,
        duration: 1.2,
        ease: "power3.out",
      });
      gsap.from(heroRef.current.querySelector("p"), {
        y: 50,
        opacity: 0,
        duration: 1,
        ease: "power2.out",
        delay: 0.3,
      });

      // Reusable card animation
      const animateCards = (selector) => {
        gsap.utils.toArray(selector).forEach((card) => {
          gsap.from(card, {
            y: 100,
            opacity: 0,
            duration: 0.8,
            ease: "power3.out",
            scrollTrigger: {
              trigger: card,
              start: "top 85%",
              toggleActions: "play none none reverse",
            },
          });
        });
      };

      animateCards(".animated-card");
    }, pageWrapperRef);

    return () => ctx.revert();
  }, []);

  // Reusable Style Objects
  const primaryColor = "#007bff";
  const primaryDarkColor = "#0056b3";
  const primaryRgb = "0,123,255";
  const glassEffect = {
    background: "rgba(255, 255, 255, 0.15)",
    backdropFilter: "blur(18px)",
    border: "1.5px solid rgba(255, 255, 255, 0.18)",
    transition: "all 0.4s cubic-bezier(.4,2,.6,1)",
  };

  return (
    <div
      className="ai-page-wrapper relative overflow-hidden"
      ref={pageWrapperRef}
      style={{
        background:
          "linear-gradient(135deg, rgba(0, 41, 86, 0.95) 0%, rgba(0, 41, 86, 0.98) 100%)",
        backdropFilter: "blur(10px)",
      }}
    >
      {/* Background Grid Pattern for AIPage, matching IoTPage */}
      <div style={{ position: "absolute", inset: 0, zIndex: 0 }}>
        <div
          style={{
            position: "absolute",
            inset: 0,
            opacity: 0.2,
            backgroundImage: `linear-gradient(rgba(0, 160, 233, 0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(0, 160, 233, 0.1) 1px, transparent 1px)`,
            backgroundSize: "50px 50px",
          }}
        />
      </div>

      {/* Hero Section */}
      <section
        ref={heroRef}
        className="ai-hero-section text-white text-center d-flex align-items-center position-relative overflow-hidden"
        style={{
          background:
            "linear-gradient(135deg, rgba(0, 41, 86, 0.95) 0%, rgba(0, 41, 86, 0.98) 100%)",
          backdropFilter: "blur(10px)",
          backgroundSize: "cover",
          backgroundPosition: "center",
          minHeight: "100vh",
          padding: "8rem 0",
        }}
      >
        <div
          className="position-absolute w-100 h-100 top-0 start-0"
          style={{ zIndex: 0, opacity: 0.5 }}
        >
          <svg width="100%" height="100%">
            {neuralNodes.map((node) => (
              <g key={node.id}>
                {node.connections.map((connId) => {
                  const connectedNode = neuralNodes[connId];
                  if (!connectedNode) return null;
                  return (
                    <line
                      key={`${node.id}-${connId}`}
                      x1={`${node.x}%`}
                      y1={`${node.y}%`}
                      x2={`${connectedNode.x}%`}
                      y2={`${connectedNode.y}%`}
                      stroke="rgba(0, 160, 233, 0.2)"
                      strokeWidth="1"
                    />
                  );
                })}
              </g>
            ))}
            {neuralNodes.map((node) => (
              <g key={node.id}>
                <circle
                  cx={`${node.x}%`}
                  cy={`${node.y}%`}
                  r={node.size}
                  fill="rgba(0, 160, 233, 0.8)"
                />
              </g>
            ))}
          </svg>
        </div>

        <Container className="position-relative" style={{ zIndex: 2 }}>
          <h1
            className="fw-bolder mb-4"
            style={{
              background: "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              textShadow: "0 0 30px rgba(0, 160, 233, 0.3)",
              fontSize: "clamp(2rem, 5vw, 4rem)",
            }}
          >
            {aiPageData.hero.title}
          </h1>
          <p
            className="mb-5 mx-auto"
            style={{
              maxWidth: "1000px",
              color: "#ffffff",
              fontSize: "clamp(1rem, 2.5vw, 1.35rem)",
            }}
          >
            {aiPageData.hero.subtitle}
          </p>
        </Container>
      </section>

      {/* Why Choose Us Section */}
      <section
        className="py-5 text-white"
        style={{
          background:
            "linear-gradient(135deg, rgba(0, 41, 86, 0.8) 0%, rgba(0, 41, 86, 0.9) 100%)",
          backdropFilter: "blur(10px)",
        }}
      >
        <Container>
          <Row className="text-center mb-5">
            <Col>
              <h2
                className="fw-bold"
                style={{
                  background:
                    "linear-gradient(135deg, #ffffff 0%, #00a0e9 80%)",
                  WebkitBackgroundClip: "text",
                  WebkitTextFillColor: "transparent",
                  textShadow: "0 0 20px rgba(0, 160, 233, 0.2)",
                  fontSize: "clamp(1.5rem, 4vw, 3rem)",
                }}
              >
                {aiPageData.whyChooseUs.title}
              </h2>
              <p
                className="lead mx-auto"
                style={{ maxWidth: "800px", color: "rgba(255,255,255,0.8)" }}
              >
                {aiPageData.whyChooseUs.description}
              </p>
            </Col>
          </Row>
          <Row className="justify-content-center">
            {" "}
            {/* Added justify-content-center for better alignment */}
            {aiPageData.whyChooseUs.items.map((item, index) => (
              <Col md={4} key={index} className="mb-4 animated-card d-flex">
                {" "}
                {/* Added d-flex for consistent card height */}
                <Card
                  className="h-100 text-center text-white why-us-card"
                  style={{ ...glassEffect, borderRadius: "20px" }}
                >
                  <Card.Body className="d-flex flex-column align-items-center justify-content-center p-3">
                    {" "}
                    {/* Centered content, increased padding */}
                    <div
                      className="mb-2 d-flex justify-content-center align-items-center"
                      style={{
                        width: "50px",
                        height: "50px",
                        borderRadius: "50%",
                        background:
                          "linear-gradient(135deg, #00a0e9 60%, #0056b3 100%)",
                        boxShadow: "0 2px 8px rgba(0,160,233,0.13)",
                      }}
                    >
                      {" "}
                      {/* Explicitly centered icon container, larger size */}
                      <i
                        className={`${item.icon}`}
                        style={{ color: "#fff", fontSize: "1.5rem" }}
                      ></i>{" "}
                      {/* Larger icon font size */}
                    </div>
                    <Card.Title
                      as="h3"
                      className="mb-2"
                      style={{ fontSize: "1.3rem" }}
                    >
                      {item.title}
                    </Card.Title>{" "}
                    {/* Increased font size and mb */}
                    <Card.Text
                      style={{
                        color: "rgba(255,255,255,0.7)",
                        fontSize: "0.9rem",
                      }}
                    >
                      {" "}
                      {/* Increased font size for text */}
                      {item.text}
                    </Card.Text>
                  </Card.Body>
                </Card>
              </Col>
            ))}
          </Row>
        </Container>
      </section>

      {/* Our Process Section */}
      <section
        ref={processRef}
        className="py-5 text-white"
        style={{
          background:
            "linear-gradient(135deg, rgba(0, 41, 86, 0.8) 0%, rgba(0, 41, 86, 0.9) 100%)",
          backdropFilter: "blur(10px)",
        }}
      >
        <Container>
          <Row className="text-center mb-5">
            <Col>
              <h2
                className="fw-bold"
                style={{
                  background:
                    "linear-gradient(135deg, #ffffff 0%, #00a0e9 80%)",
                  WebkitBackgroundClip: "text",
                  WebkitTextFillColor: "transparent",
                  textShadow: "0 0 20px rgba(0, 160, 233, 0.2)",
                  fontSize: "clamp(1.5rem, 4vw, 3rem)",
                }}
              >
                {aiPageData.process.title}
              </h2>
              <p
                className="lead mx-auto"
                style={{ maxWidth: "800px", color: "rgba(255,255,255,0.8)" }}
              >
                {aiPageData.process.description}
              </p>
            </Col>
          </Row>
          <Row className="justify-content-center">
            {" "}
            {/* Added justify-content-center for better alignment */}
            {aiPageData.process.steps.map((step, index) => (
              <Col
                key={index}
                md={4}
                lg={2}
                className="mb-4 d-flex align-items-stretch animated-card"
              >
                {" "}
                {/* Changed lg to lg={2} for smaller cards */}
                <div
                  className="process-step-card d-flex flex-column align-items-center text-center w-100 p-3"
                  style={{ ...glassEffect, borderRadius: "20px" }}
                >
                  {" "}
                  {/* Increased padding, ensured borderRadius */}
                  {/* REMOVED: <div className="process-step-number mb-3"><span>0{index + 1}</span></div> */}
                  <i
                    className={`${step.icon} mb-2`}
                    style={{ color: "#00a0e9", fontSize: "1.4rem" }}
                  ></i>{" "}
                  {/* Increased icon font size */}
                  <h4
                    className="mb-2 text-white"
                    style={{ fontSize: "clamp(1rem, 2vw, 1.2rem)" }}
                  >
                    {step.title}
                  </h4>{" "}
                  {/* Increased font size and mb */}
                  <p
                    className="mb-0"
                    style={{
                      color: "rgba(255,255,255,0.7)",
                      fontSize: "0.85rem",
                    }}
                  >
                    {step.text}
                  </p>{" "}
                  {/* Increased font size */}
                </div>
              </Col>
            ))}
          </Row>
        </Container>
      </section>

      {/* Industries Section */}
      <section
        className="py-5 text-white"
        style={{
          background:
            "linear-gradient(135deg, rgba(0, 41, 86, 0.8) 0%, rgba(0, 41, 86, 0.9) 100%)",
          backdropFilter: "blur(10px)",
        }}
      >
        <Container>
          <Row className="text-center mb-5">
            <Col>
              <h2
                className="fw-bold"
                style={{
                  background:
                    "linear-gradient(135deg, #ffffff 0%, #00a0e9 80%)",
                  WebkitBackgroundClip: "text",
                  WebkitTextFillColor: "transparent",
                  textShadow: "0 0 20px rgba(0, 160, 233, 0.2)",
                  fontSize: "clamp(1.5rem, 4vw, 3rem)",
                }}
              >
                {aiPageData.industries.title}
              </h2>
              <p
                className="lead mx-auto"
                style={{ maxWidth: "800px", color: "rgba(255,255,255,0.8)" }}
              >
                {aiPageData.industries.description}
              </p>
            </Col>
          </Row>
          <Row className="justify-content-center">
            {" "}
            {/* Added justify-content-center for better alignment */}
            {aiPageData.industries.items.map((industry, index) => (
              <Col
                md={6}
                lg={3}
                key={index}
                className="mb-4 animated-card d-flex"
              >
                {" "}
                {/* Added d-flex for consistent card height */}
                <Card
                  className="h-100 text-white industry-card"
                  style={{ ...glassEffect, borderRadius: "20px" }}
                >
                  <Card.Body className="d-flex flex-column text-center align-items-center justify-content-center p-3">
                    {" "}
                    {/* Centered content, increased padding */}
                    <div
                      className="mb-2 d-flex justify-content-center align-items-center"
                      style={{
                        width: "50px",
                        height: "50px",
                        borderRadius: "50%",
                        background:
                          "linear-gradient(135deg, #00a0e9 60%, #0056b3 100%)",
                        boxShadow: "0 2px 8px rgba(0,160,233,0.13)",
                      }}
                    >
                      {" "}
                      {/* Explicitly centered icon container, larger size */}
                      <i
                        className={`${industry.icon}`}
                        style={{ color: "#fff", fontSize: "1.5rem" }}
                      ></i>{" "}
                      {/* Larger icon font size */}
                    </div>
                    <Card.Title
                      as="h4"
                      className="mb-2"
                      style={{ fontSize: "1.3rem" }}
                    >
                      {industry.name}
                    </Card.Title>{" "}
                    {/* Increased font size and mb */}
                    <Card.Text
                      style={{
                        color: "rgba(255,255,255,0.7)",
                        fontSize: "0.9rem",
                      }}
                    >
                      {" "}
                      {/* Increased font size */}
                      {industry.description}
                    </Card.Text>
                  </Card.Body>
                </Card>
              </Col>
            ))}
          </Row>
        </Container>
      </section>

      {/* FAQ Section */}
      <section
        className="py-5 text-white position-relative"
        style={{
          background:
            "linear-gradient(135deg, rgba(0, 41, 86, 0.8) 0%, rgba(0, 41, 86, 0.9) 100%)",
          backdropFilter: "blur(10px)",
          zIndex: 2,
        }}
      >
        <Container style={{ position: "relative", zIndex: 3 }}>
          <Row className="text-center mb-5">
            <Col>
              <h2
                className="fw-bold"
                style={{
                  background:
                    "linear-gradient(135deg, #ffffff 0%, #00a0e9 80%)",
                  WebkitBackgroundClip: "text",
                  WebkitTextFillColor: "transparent",
                  textShadow: "0 0 20px rgba(0, 160, 233, 0.2)",
                  fontSize: "clamp(1.5rem, 4vw, 3rem)",
                }}
              >
                {aiPageData.faq.title}
              </h2>
            </Col>
          </Row>
          <Row>
            <div
              style={{
                background: "rgba(10,20,40,0.85)",
                borderRadius: "18px",
                padding: "2.5rem 2rem",
                zIndex: 4,
                width: "80%",
                margin: "0 auto",
              }}
            >
              <Accordion defaultActiveKey="0" className="faq-accordion">
                {aiPageData.faq.items.map((item, index) => (
                  <Accordion.Item
                    eventKey={String(index)}
                    key={index}
                    className="mb-3 animated-card"
                    style={{ ...glassEffect, borderRadius: "15px" }}
                  >
                    <Accordion.Header
                      style={{ background: "transparent", color: "white" }}
                    >
                      {item.question}
                    </Accordion.Header>
                    <Accordion.Body
                      style={{
                        color: "rgba(255,255,255,0.9)",
                        background: "rgba(255,255,255,0.07)",
                      }}
                    >
                      {item.answer}
                    </Accordion.Body>
                  </Accordion.Item>
                ))}
              </Accordion>
            </div>
          </Row>
        </Container>
      </section>

      {/* CTA Section - This entire section is removed */}
      {/* <section className="py-5 text-white text-center" style={{ background: "linear-gradient(135deg, rgba(0, 41, 86, 0.9) 0%, rgba(0, 41, 86, 0.95) 100%)", backdropFilter: "blur(10px)" }}>
        <Container>
          <h2 className="display-4 fw-bold mb-4" style={{ background: "linear-gradient(135deg, #ffffff 0%, #00a0e9 80%)", WebkitBackgroundClip: "text", WebkitTextFillColor: "transparent", textShadow: "0 0 20px rgba(0, 160, 233, 0.2)" }}>
            {aiPageData.cta.title}
          </h2>
          <p className="lead mb-4 mx-auto" style={{ maxWidth: "800px", color: "rgba(255,255,255,0.8)" }}>{aiPageData.cta.text}</p>
          <Link to={aiPageData.cta.buttonLink}>
            <Button variant="primary" size="lg" className="animate__animated animate__pulse animate__infinite" style={{ background: "#00a0e9", border: "none", padding: "12px 30px", borderRadius: "50px", fontWeight: "600", boxShadow: "0 8px 20px rgba(0, 160, 233, 0.3)" }}>
              {aiPageData.cta.buttonText}
            </Button>
          </Link>
        </Container>
      </section> */}

      {/* Global CSS */}
      <style>{`
        :root {
          --bs-primary: ${primaryColor};
          --bs-primary-dark: ${primaryDarkColor};
          --bs-primary-rgb: ${primaryRgb};
        }

        /* Glassmorphism for cards */
        .why-us-card, .industry-card, .process-step-card {
          background: rgba(255, 255, 255, 0.15) !important;
          backdrop-filter: blur(18px) !important;
          border: 1.5px solid rgba(255,255,255,0.18) !important;
          box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.18), 0 1.5px 6px 0 rgba(0,0,0,0.10);
          transition: all 0.4s cubic-bezier(.4,2,.6,1);
          /* Adjusted padding for better fit */
          padding: 1.5rem !important; /* Increased padding for better information fit */
          border-radius: 20px !important; /* Ensure curved edges */
        }
        .why-us-card:hover, .industry-card:hover, .process-step-card:hover {
          transform: scale(1.045) translateY(-8px);
          background: rgba(0, 160, 233, 0.15) !important;
          box-shadow: 0 16px 48px 0 rgba(0, 160, 233, 0.18), 0 4px 16px 0 rgba(0,0,0,0.13);
        }
        .why-us-card, .industry-card, .process-step-card {
          margin-bottom: 2rem !important; /* Adjusted margin-bottom */
        }
        /* Icon circle backgrounds and sizes */
        .why-us-card .mb-2 > i, .industry-card .mb-2 > i, .process-step-card .mb-2 > i { /* Adjusted selector for mb-2 */
          background: linear-gradient(135deg, #00a0e9 60%, #0056b3 100%);
          color: #fff !important;
          border-radius: 50%;
          width: 64px; /* Restored larger icon circle size */
          height: 64px; /* Restored larger icon circle size */
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 0 2px 8px rgba(0,160,233,0.13);
          font-size: 2rem !important; /* Restored larger icon font size */
        }
        /* Specific icon sizes for consistency */
        .why-us-card .fa-3x, .industry-card .fa-3x {
          font-size: 2rem !important; /* Matched with the circle size */
        }
        .process-step-card .fa-2x {
          font-size: 1.7rem !important; /* Adjusted icon for process steps */
        }
        .why-us-card .card-title, .industry-card .card-title, .process-step-card h4 {
          margin-top: 1.2rem; /* Adjusted margin */
          margin-bottom: 1rem; /* Adjusted margin */
          font-weight: 600;
          letter-spacing: 0.01em;
          font-size: 1.6rem; /* Adjusted font size */
        }
        .why-us-card .card-text, .industry-card .card-text, .process-step-card p {
          font-size: 1.08rem; /* Adjusted font size */
          line-height: 1.7; /* Adjusted line height */
          color: rgba(255,255,255,0.8) !important;
        }
        /* FAQ Accordion glassy style - RESTORED TO PREVIOUS GOOD STATE */
        .faq-accordion .accordion-item {
          border-radius: 18px !important;
          margin-bottom: 1.2rem !important; /* Reverted margin */
          overflow: hidden;
        }
        .faq-accordion .accordion-header button {
          background: transparent;
          color: #fff;
          font-weight: bold;
          border: none;
          box-shadow: none;
          font-size: 1.15rem; /* Reverted font size */
          padding: 1.2rem 1.5rem; /* Reverted padding */
          transition: background 0.25s;
        }
        .faq-accordion .accordion-header button:not(.collapsed) {
          color: #00a0e9;
          background: rgba(0, 160, 233, 0.15);
        }
        .faq-accordion .accordion-header button:focus {
          outline: 2px solid #00a0e9;
          outline-offset: 2px;
        }
        .faq-accordion .accordion-header button::after {
          filter: invert(1) grayscale(100%) brightness(250%);
        }
        .faq-accordion .accordion-body {
          background: rgba(255,255,255,0.07);
          color: rgba(255,255,255,0.9) !important; /* Reverted color opacity */
          padding: 1.5rem 2rem; /* Reverted padding */
          font-size: 1.08rem; /* Reverted font size */
          border-top: 1px solid rgba(0,160,233,0.08);
          transition: background 0.25s;
        }
        .faq-accordion .accordion-item.active {
          background: rgba(0,160,233,0.15) !important;
        }
        /* Fallback for browsers without backdrop-filter support */
        @supports not (backdrop-filter: blur(18px)) {
          .why-us-card, .industry-card, .process-step-card, .faq-accordion .accordion-item {
            background: rgba(10,20,40,0.9) !important;
          }
        }
        /* Mobile optimizations */
        @media (max-width: 768px) {
          .why-us-card, .industry-card, .process-step-card, .faq-accordion .accordion-item {
            padding: 1rem !important; /* Adjusted padding for mobile */
          }
          .faq-accordion .accordion-body {
            padding: 1rem 1.5rem; /* Adjusted padding for mobile */
          }
          .ai-hero-section h1.display-1 {
            font-size: 2.5rem; /* Adjusted for smaller mobile screens */
          }
          .ai-hero-section p.lead {
            font-size: 1.1rem; /* Adjusted for smaller mobile screens */
          }
        }
      `}</style>
    </div>
  );
};

export default AIPage;
