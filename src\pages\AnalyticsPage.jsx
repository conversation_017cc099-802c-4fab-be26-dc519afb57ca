import React, { useEffect, useState, useRef } from 'react';
import { Link } from 'react-router-dom';
import { Container, Row, Col, Button, Nav } from 'react-bootstrap';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import Analytics1 from "../assets/Analytics1.jpg";
import Analytics2 from "../assets/Analytics2.jpg";
import Analytics3 from "../assets/Analytics3.jpg";
import Analytics4 from "../assets/Analytics4.jpg";
import Analytics5 from "../assets/Analytics5.jpg";

gsap.registerPlugin(ScrollTrigger);

const AnalyticsPage = () => {
  const [activeSection, setActiveSection] = useState('customer');
  const [isIntersecting, setIsIntersecting] = useState({});
  const heroRef = useRef(null);
  const dashboardRef = useRef(null); // This ref isn't used in the provided code for actual elements
  const sectionRefs = {
    intro: useRef(null),
    customer: useRef(null),
    risk: useRef(null),
    finance: useRef(null),
    hr: useRef(null)
  };

  useEffect(() => {
    window.scrollTo(0, 0);

    // GSAP Animations
    const ctx = gsap.context(() => {
      // Hero section animations
      gsap.from(heroRef.current.querySelector('h1'), {
        y: 100,
        opacity: 0,
        duration: 1.2,
        ease: "power3.out"
      });

      gsap.from(heroRef.current.querySelector('p'), {
        y: 50,
        opacity: 0,
        duration: 1,
        ease: "power2.out",
        delay: 0.3
      });

      gsap.from(heroRef.current.querySelectorAll('button, a'), {
        y: 30,
        opacity: 0,
        duration: 0.8,
        stagger: 0.2,
        ease: "back.out(1.7)",
        delay: 0.6
      });

      // Dashboard animation (Note: dashboardRef is not linked to any element in the JSX)
      // If you intend to animate a dashboard element, ensure it has a ref={dashboardRef}
      gsap.from(dashboardRef.current, {
        x: 100,
        opacity: 0,
        duration: 1.5,
        ease: "power2.out",
        delay: 0.8
      });

      // Stats animation (Note: '.stat-item' class is not found in the provided JSX)
      // If you intend to animate elements with this class, add them to your JSX.
      gsap.from('.stat-item', {
        scale: 0,
        opacity: 0,
        duration: 0.6,
        stagger: 0.1,
        ease: "back.out(1.7)",
        delay: 1.2
      });

    }, heroRef);

    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.5
    };

    const observerCallback = (entries) => {
      entries.forEach(entry => {
        setIsIntersecting(prev => ({
          ...prev,
          [entry.target.id]: entry.isIntersecting
        }));

        if (entry.isIntersecting && entry.target.id !== 'intro') {
          setActiveSection(entry.target.id);
        }
      });
    };

    const observer = new IntersectionObserver(observerCallback, observerOptions);

    Object.values(sectionRefs).forEach(ref => {
      if (ref.current) {
        observer.observe(ref.current);
      }
    });

    return () => {
      ctx.revert();
      Object.values(sectionRefs).forEach(ref => {
        if (ref.current) {
          observer.unobserve(ref.current);
        }
      });
    };
  }, []);

  const scrollToSection = (sectionId) => {
    setActiveSection(sectionId);
    sectionRefs[sectionId].current.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <div
      className="min-h-screen relative overflow-hidden"
      style={{
        background: "linear-gradient(135deg, rgba(0, 41, 86, 0.95) 0%, rgba(0, 41, 86, 0.98) 100%)",
        backdropFilter: "blur(10px)",
        overflowX: 'hidden'
      }}
    >
      {/* Background Grid Pattern */}
      <div className="absolute inset-0 z-0">
        <div
          className="absolute inset-0 opacity-20"
          style={{
            backgroundImage: `linear-gradient(rgba(0, 160, 233, 0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(0, 160, 233, 0.1) 1px, transparent 1px)`,
            backgroundSize: "50px 50px",
          }}
        />
        {/* Animated Background Icons */}
        {[
          {
            icon: "fa-chart-pie",
            top: "10%",
            left: "5%",
            delay: 0,
          },
          {
            icon: "fa-chart-line",
            top: "20%",
            right: "8%",
            delay: 1,
          },
          {
            icon: "fa-database",
            bottom: "15%",
            left: "3%",
            delay: 2,
          },
          {
            icon: "fa-brain",
            bottom: "25%",
            right: "10%",
            delay: 3,
          },
        ].map((item, index) => (
          <div
            key={index}
            className="position-absolute"
            style={{
              top: item.top,
              left: item.left,
              right: item.right,
              bottom: item.bottom,
              animation: `float 6s ease-in-out infinite`,
              animationDelay: `${item.delay}s`,
              opacity: 0.1,
            }}
          >
            <i
              className={`fas ${item.icon} text-white`}
              style={{ fontSize: "2rem" }}
            ></i>
          </div>
        ))}
      </div>
      <div className="relative z-10">
      <section
        ref={heroRef}
        className="analytics-hero-section text-white text-center d-flex align-items-center position-relative overflow-hidden"
        style={{
          backgroundImage: `linear-gradient(rgba(0, 41, 86, 0.85), rgba(0, 41, 86, 0.95)), url("https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80")`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          minHeight: "100vh",
          padding: "8rem 0",
        }}
      >
        {/* Animated Background Elements */}
        <div className="position-absolute w-100 h-100" style={{ zIndex: 1 }}>
          {/* Floating Analytics Icons */}
          {[
            // { icon: "fa-chart-pie", top: "15%", left: "10%", delay: 0 },
            // { icon: "fa-chart-line", top: "25%", right: "15%", delay: 1 },
            // { icon: "fa-database", bottom: "20%", left: "8%", delay: 2 },
            // { icon: "fa-brain", bottom: "30%", right: "12%", delay: 3 },
          ].map((item, index) => (
            <div
              key={index}
              className="position-absolute"
              style={{
                ...item,
                width: "60px",
                height: "60px",
                background: "rgba(0, 160, 233, 0.1)",
                borderRadius: "50%",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                backdropFilter: "blur(10px)",
                border: "1px solid rgba(0, 160, 233, 0.2)",
                animation: `float 6s ease-in-out infinite`,
                animationDelay: `${item.delay}s`,
              }}
            >
              <i
                className={`fas ${item.icon}`}
                style={{
                  fontSize: "24px",
                  color: "#00a0e9",
                  textShadow: "0 0 10px rgba(0, 160, 233, 0.5)",
                }}
              ></i>
            </div>
          ))}

          {/* Pulse Circles */}
          {[
            // { size: "200px", top: "10%", right: "20%", delay: "0s" },
            // { size: "150px", bottom: "15%", left: "15%", delay: "2s" },
            // { size: "100px", top: "60%", right: "10%", delay: "4s" },
          ].map((circle, index) => (
            <div
              key={index}
              className="position-absolute rounded-circle"
              style={{
                width: circle.size,
                height: circle.size,
                top: circle.top,
                bottom: circle.bottom,
                left: circle.left,
                right: circle.right,
                background: "rgba(0, 160, 233, 0.05)",
                border: "1px solid rgba(0, 160, 233, 0.1)",
                animation: `pulse 4s ease-in-out infinite`,
                animationDelay: circle.delay,
              }}
            ></div>
          ))}
        </div>

        <Container className="position-relative" style={{ zIndex: 2 }}>
          <h1
            className="fw-bolder mb-4 animate__animated animate__fadeInDown animate__slow"
            style={{
              fontSize: "clamp(1.875rem, 5vw, 3rem)",
              fontWeight: "800",
              letterSpacing: "2.6px",
              background: "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              backgroundClip: "text",
              textShadow: "0 0 30px rgba(0, 160, 233, 0.3)",
            }}
          >
            Transform Your Data into Decisions
          </h1>
          <p
            className="mb-5 mx-auto animate__animated animate__fadeInUp animate__slow"
            style={{
              maxWidth: "1200px",
              textShadow: "1px 1px 3px rgba(0,0,0,0.4)",
              fontSize: "clamp(1rem, 2.5vw, 1.35rem)",
            }}
          >
           We combine in-depth industry expertise with world-class technical knowledge to help you create compelling software-based products and unlock the full potential of your data. We partner with you to design scalable, high-performance solutions tailored to your needs. Our team ensures seamless integration, robust security, and long-term value for your business.
          </p>

        </Container>
      </section>

      {/* Feature Sections */}
      <div
        className="py-5 py-md-6"
        style={{
          background:
            "linear-gradient(135deg, rgba(0, 41, 86, 0.8) 0%, rgba(0, 41, 86, 0.9) 100%)",
          backdropFilter: "blur(10px)",
        }}
      >
        <Container>
          <section
            id="intro"
            ref={sectionRefs.intro}
            className="mb-5 mb-md-6 py-3"
            style={{
              opacity: isIntersecting.intro ? 1 : 0,
              transform: isIntersecting.intro ? 'translateY(0)' : 'translateY(30px)',
              transition: 'opacity 0.8s ease, transform 0.8s ease'
            }}
          >
            {/* Centered Heading Above Content */}
            <Row className="mb-4">
              <Col xs={12}>
                <h2
                  className="display-5 fw-bold text-center"
                  style={{
                    fontSize: "clamp(1.75rem, 4vw, 2.25rem)",
                    fontWeight: "800",
                    letterSpacing: "2.6px",
                    background:
                      "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
                    WebkitBackgroundClip: "text",
                    WebkitTextFillColor: "transparent",
                    backgroundClip: "text",
                    textShadow: "0 0 30px rgba(0, 160, 233, 0.3)",
                  }}
                >
                  Transforming Data into Business Insights
                </h2>
              </Col>
            </Row>

            {/* Enhanced Accent Line */}
            <div className="w-30 h-1 mx-auto relative mb-5">
              <div
                className="w-full h-full rounded-sm shadow-glow"
                style={{
                  background: 'linear-gradient(90deg, transparent, #00a0e9, transparent)',
                }}
              />
            </div>

            {/* Content and Image Row */}
            <Row className="align-items-center g-4 g-lg-5">
              <Col lg={6} md={10} className="pe-lg-4">
                <div className="content-wrapper">
                  <p
                    className="mb-3 mb-md-4"
                    style={{
                      fontSize: "1.2rem",
                      lineHeight: "1.7",
                      color: "rgba(255, 255, 255, 0.9)",
                      textAlign: "justify",
                    }}
                  >
                    <strong>Makonis</strong>, as a Business Intelligence and Analytics company, we have extensive experience delivering BI, Big Data, and Data Science projects, including data integration, governance, reporting, ad-hoc analysis, migration, real-time and mobile BI, cloud BI, and production support.
                  </p>
                  <p
                    className="mb-3 mb-md-4"
                    style={{
                      fontSize: "1.2rem",
                      lineHeight: "1.7",
                      color: "rgba(255, 255, 255, 0.9)",
                      textAlign: "justify",
                    }}
                  >
                    Our data analytics consulting services uncover hidden opportunities and improve business operations. We deliver information management, business intelligence, and analytics solutions under one roof.
                  </p>
                </div>
              </Col>
              <Col lg={6} md={10} className="ps-lg-4">
                <div
                  className="image-container"
                  style={{
                    borderRadius: "1.25rem",
                    overflow: "hidden",
                    boxShadow: "0 0.75rem 2rem rgba(0,0,0,0.1)",
                    transition: "transform 0.4s ease-out, box-shadow 0.4s ease-out",
                    backgroundColor: "#f0f2f5",
                  }}
                >
                  <img
                    src={Analytics5}
                    alt="Analytics and Business Intelligence"
                    style={{
                      width: "100%",
                      height: "100%",
                      minHeight: "400px",
                      objectFit: "cover",
                      transition: "transform 0.6s ease",
                    }}
                  />
                </div>
              </Col>
            </Row>
          </section>

          <section
            id="customer"
            ref={sectionRefs.customer}
            className="mb-5 mb-md-6 py-3"
            style={{
              opacity: isIntersecting.customer ? 1 : 0,
              transform: isIntersecting.customer ? 'translateY(0)' : 'translateY(30px)',
              transition: 'opacity 0.8s ease, transform 0.8s ease'
            }}
          >
            {/* Centered Heading Above Content */}
            <Row className="mb-4">
              <Col xs={12}>
                <h2
                  className="fw-bold text-center"
                  style={{
                    fontSize: "clamp(1.5rem, 4vw, 2.25rem)",
                    fontWeight: "800",
                    letterSpacing: "2.6px",
                    background:
                      "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
                    WebkitBackgroundClip: "text",
                    WebkitTextFillColor: "transparent",
                    backgroundClip: "text",
                    textShadow: "0 0 30px rgba(0, 160, 233, 0.3)",
                  }}
                >
                  Customer Analytics
                </h2>
              </Col>
            </Row>

            {/* Enhanced Accent Line */}
            <div className="w-30 h-1 mx-auto relative mb-5">
              <div
                className="w-full h-full rounded-sm shadow-glow"
                style={{
                  background: 'linear-gradient(90deg, transparent, #00a0e9, transparent)',
                }}
              />
            </div>

            {/* Content and Image Row */}
            <Row className="align-items-center g-4 g-lg-5">
              <Col lg={6} md={10} className="pe-lg-4">
                <div className="content-wrapper">
                  <p
                    className="mb-3 mb-md-4"
                    style={{
                      fontSize: "1.2rem",
                      lineHeight: "1.7",
                      color: "rgba(255, 255, 255, 0.9)",
                      textAlign: "justify",
                    }}
                  >
                   Customer analytics is the systematic analysis of customer data and behavior to identify, attract, and retain the most valuable customers.
                  </p>
                  <p
                    className="mb-3 mb-md-4"
                    style={{
                      fontSize: "1.2rem",
                      lineHeight: "1.7",
                      color: "rgba(255, 255, 255, 0.9)",
                      textAlign: "justify",
                    }}
                  >
                  The goal of customer analytics is to create an accurate customer view to improve acquisition, retention, and engagement with high-value customers.
                  </p>
                  <ul
                    className="list-unstyled mt-4"
                    style={{
                      fontSize: "1.2rem",
                      lineHeight: "1.8",
                      color: "rgba(255, 255, 255, 0.9)",
                    }}
                  >
                    {[
                      "Pricing Model optimization",
                      "Churn Analysis and prevention",
                      // "Data Segmentation strategies",
                      // "Customer Loyalty modeling"
                    ].map((item, index) => (
                      <li
                        key={index}
                        className="mb-1 d-flex align-items-center" // Changed mb-3 to mb-1
                        style={{
                          padding: "0.8rem 0",
                        }}
                      >
                        <div
                          className="me-3 d-flex align-items-center justify-content-center"
                          style={{
                            width: "40px",
                            height: "40px",
                            borderRadius: "50%",
                            background: "rgba(0, 160, 233, 0.2)",
                            border: "1px solid rgba(0, 160, 233, 0.3)",
                          }}
                        >
                          <i
                            className="fas fa-check"
                            style={{
                              color: "#00a0e9",
                              fontSize: "16px",
                            }}
                          ></i>
                        </div>
                        {item}
                      </li>
                    ))}
                  </ul>
                </div>
              </Col>
              <Col lg={6} md={10} className="ps-lg-4">
                <div
                  className="image-container"
                  style={{
                    borderRadius: "1.25rem",
                    overflow: "hidden",
                    boxShadow: "0 0.75rem 2rem rgba(0,0,0,0.1)",
                    transition: "transform 0.4s ease-out, box-shadow 0.4s ease-out",
                    backgroundColor: "#f0f2f5",
                  }}
                >
                  <img
                    src={Analytics1}
                    alt="Customer Analytics Dashboard"
                    style={{
                      width: "100%",
                      height: "100%",
                      minHeight: "400px",
                      objectFit: "cover",
                      transition: "transform 0.6s ease",
                    }}
                  />
                </div>
              </Col>
            </Row>
          </section>

          <section
            id="risk"
            ref={sectionRefs.risk}
            className="mb-5 mb-md-6 py-3"
            style={{
              opacity: isIntersecting.risk ? 1 : 0,
              transform: isIntersecting.risk ? 'translateY(0)' : 'translateY(30px)',
              transition: 'opacity 0.8s ease, transform 0.8s ease'
            }}
          >
            {/* Centered Heading Above Content */}
            <Row className="mb-4">
              <Col xs={12}>
                <h2
                  className="fw-bold text-center"
                  style={{
                    fontSize: "clamp(1.5rem, 4vw, 2.25rem)",
                    fontWeight: "800",
                    letterSpacing: "2.6px",
                    background:
                      "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
                    WebkitBackgroundClip: "text",
                    WebkitTextFillColor: "transparent",
                    backgroundClip: "text",
                    textShadow: "0 0 30px rgba(0, 160, 233, 0.3)",
                  }}
                >
                  Risk Analytics
                </h2>
              </Col>
            </Row>

            {/* Enhanced Accent Line */}
            <div className="w-30 h-1 mx-auto relative mb-5">
              <div
                className="w-full h-full rounded-sm shadow-glow"
                style={{
                  background: 'linear-gradient(90deg, transparent, #00a0e9, transparent)',
                }}
              />
            </div>

            {/* Content and Image Row - Reversed */}
            <Row className="align-items-center g-4 g-lg-5 flex-row-reverse">
              <Col lg={6} md={10} className="ps-lg-4">
                <div className="content-wrapper">
                  <p
                    className="mb-3 mb-md-4"
                    style={{
                      fontSize: "1.2rem",
                      lineHeight: "1.7",
                      color: "rgba(255, 255, 255, 0.9)",
                      textAlign: "justify",
                    }}
                  > 
                  Risk analytics is the study of uncertainty in decisions, working with forecasting to minimize unforeseen negative effects.
                  </p>
                  <p
                    className="mb-3 mb-md-4"
                    style={{
                      fontSize: "1.2rem",
                      lineHeight: "1.7",
                      color: "rgba(255, 255, 255, 0.9)",
                      textAlign: "justify",
                    }}
                  >
                     You can reduce risks by defining, understanding, and managing risk tolerance and exposure. Advanced analytics provides clearer insight into these challenges.
                  </p>

                  <ul
                    className="list-unstyled mt-4"
                    style={{
                      fontSize: "1.2rem",
                      lineHeight: "1.8",
                      color: "rgba(255, 255, 255, 0.9)",
                    }}
                  >
                    {[
                      "Improve decision making with risk analysis and transparency",
                      "Reduce the cost of regulatory compliance",
                      //"Dynamically evolve with adaptable risk architecture"
                    ].map((item, index) => (
                      <li
                        key={index}
                        className="mb-1 d-flex align-items-center" // Changed mb-3 to mb-1
                        style={{
                          padding: "0.8rem 0",
                        }}
                      >
                        <div
                          className="me-3 d-flex align-items-center justify-content-center"
                          style={{
                            width: "40px",
                            height: "40px",
                            borderRadius: "50%",
                            background: "rgba(0, 160, 233, 0.2)",
                            border: "1px solid rgba(0, 160, 233, 0.3)",
                          }}
                        >
                          <i
                            className="fas fa-shield-alt"
                            style={{
                              color: "#00a0e9",
                              fontSize: "16px",
                            }}
                          ></i>
                        </div>
                        {item}
                      </li>
                    ))}
                  </ul>
                </div>
              </Col>
              <Col lg={6} md={10} className="pe-lg-4">
                <div
                  className="image-container"
                  style={{
                    borderRadius: "1.25rem",
                    overflow: "hidden",
                    boxShadow: "0 0.75rem 2rem rgba(0,0,0,0.1)",
                    transition: "transform 0.4s ease-out, box-shadow 0.4s ease-out",
                    backgroundColor: "#f0f2f5",
                  }}
                >
                  <img
                    src={Analytics2}
                    alt="Risk Analytics and Management"
                    style={{
                      width: "100%",
                      height: "100%",
                      minHeight: "400px",
                      objectFit: "cover",
                      transition: "transform 0.6s ease",
                    }}
                  />
                </div>
              </Col>
            </Row>
          </section>

          <section
            id="finance"
            ref={sectionRefs.finance}
            className="mb-5 mb-md-6 py-3"
            style={{
              opacity: isIntersecting.finance ? 1 : 0,
              transform: isIntersecting.finance ? 'translateY(0)' : 'translateY(30px)',
              transition: 'opacity 0.8s ease, transform 0.8s ease'
            }}
          >
            {/* Centered Heading Above Content */}
            <Row className="mb-4">
              <Col xs={12}>
                <h2
                  className="fw-bold text-center"
                  style={{
                    fontSize: "clamp(1.5rem, 4vw, 2.25rem)",
                    fontWeight: "800",
                    letterSpacing: "2.6px",
                    background:
                      "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
                    WebkitBackgroundClip: "text",
                    WebkitTextFillColor: "transparent",
                    backgroundClip: "text",
                    textShadow: "0 0 30px rgba(0, 160, 233, 0.3)",
                  }}
                >
                  Finance Analytics
                </h2>
              </Col>
            </Row>

            {/* Enhanced Accent Line */}
            <div className="w-30 h-1 mx-auto relative mb-5">
              <div
                className="w-full h-full rounded-sm shadow-glow"
                style={{
                  background: 'linear-gradient(90deg, transparent, #00a0e9, transparent)',
                }}
              />
            </div>

            {/* Content and Image Row */}
            <Row className="align-items-center g-4 g-lg-5">
              <Col lg={6} md={10} className="pe-lg-4">
                <div className="content-wrapper">
                  <p
                    className="mb-3 mb-md-4"
                    style={{
                      fontSize: "1.2rem",
                      lineHeight: "1.7",
                      color: "rgba(255, 255, 255, 0.9)",
                      textAlign: "justify",
                    }}
                  >
                   Digitalization in the financial economy is driving the need to store and efficiently manage large volumes of data.
                  </p>
                  <p
                    className="mb-3 mb-md-4"
                    style={{
                      fontSize: "1.2rem",
                      lineHeight: "1.7",
                      color: "rgba(255, 255, 255, 0.9)",
                      textAlign: "justify",
                    }}
                  >
               Financial analysis assesses an entity's stability, solvency, liquidity, and profitability, focusing on the income statement, balance sheet, and cash flow.
                  </p>
                  <ul
                    className="list-unstyled mt-4"
                    style={{
                      fontSize: "1.2rem",
                      lineHeight: "1.8",
                      color: "rgba(255, 255, 255, 0.9)",
                    }}
                  >
                    {[
                      "Cash Flow Analysis and forecasting",
                      "ROI Analysis and optimization",
                      // "P & L Analysis and reporting",
                      // "Revenue Analysis and growth strategies"
                    ].map((item, index) => (
                      <li
                        key={index}
                        className="mb-1 d-flex align-items-center" // Changed mb-3 to mb-1
                        style={{
                          padding: "0.8rem 0",
                        }}
                      >
                        <div
                          className="me-3 d-flex align-items-center justify-content-center"
                          style={{
                            width: "40px",
                            height: "40px",
                            borderRadius: "50%",
                            background: "rgba(0, 160, 233, 0.2)",
                            border: "1px solid rgba(0, 160, 233, 0.3)",
                          }}
                        >
                          <i
                            className="fas fa-chart-line"
                            style={{
                              color: "#00a0e9",
                              fontSize: "16px",
                            }}
                          ></i>
                        </div>
                        {item}
                      </li>
                    ))}
                  </ul>
                </div>
              </Col>
              <Col lg={6} md={10} className="ps-lg-4">
                <div
                  className="image-container"
                  style={{
                    borderRadius: "1.25rem",
                    overflow: "hidden",
                    boxShadow: "0 0.75rem 2rem rgba(0,0,0,0.1)",
                    transition: "transform 0.4s ease-out, box-shadow 0.4s ease-out",
                    backgroundColor: "#f0f2f5",
                  }}
                >
                  <img
                    src={Analytics3}
                    alt="Finance Analytics and Reporting"
                    style={{
                      width: "100%",
                      height: "100%",
                      minHeight: "400px",
                      objectFit: "cover",
                      transition: "transform 0.6s ease",
                    }}
                  />
                </div>
              </Col>
            </Row>
          </section>

          <section
            id="hr"
            ref={sectionRefs.hr}
            className="mb-5 mb-md-6 py-3"
            style={{
              opacity: isIntersecting.hr ? 1 : 0,
              transform: isIntersecting.hr ? 'translateY(0)' : 'translateY(30px)',
              transition: 'opacity 0.8s ease, transform 0.8s ease'
            }}
          >
            {/* Centered Heading Above Content */}
            <Row className="mb-4">
              <Col xs={12}>
                <h2
                  className="fw-bold text-center"
                  style={{
                    fontSize: "clamp(1.5rem, 4vw, 2.25rem)",
                    fontWeight: "800",
                    letterSpacing: "2.6px",
                    background:
                      "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
                    WebkitBackgroundClip: "text",
                    WebkitTextFillColor: "transparent",
                    backgroundClip: "text",
                    textShadow: "0 0 30px rgba(0, 160, 233, 0.3)",
                  }}
                >
                  HR Analytics
                </h2>
              </Col>
            </Row>

            {/* Enhanced Accent Line */}
            <div className="w-30 h-1 mx-auto relative mb-5">
              <div
                className="w-full h-full rounded-sm shadow-glow"
                style={{
                  background: 'linear-gradient(90deg, transparent, #00a0e9, transparent)',
                }}
              />
            </div>

            {/* Content and Image Row - Reversed */}
            <Row className="align-items-center g-4 g-lg-5 flex-row-reverse">
              <Col lg={6} md={10} className="ps-lg-4">
                <div className="content-wrapper">
                  <p
                    className="mb-3 mb-md-4"
                    style={{
                      fontSize: "1.2rem",
                      lineHeight: "1.7",
                      color: "rgba(255, 255, 255, 0.9)",
                      textAlign: "justify",
                    }}
                  >
                    Human resource analytics applies analytic processes to HR to improve employee performance and boost return on investment.
                  </p>
                  <p
                    className="mb-3 mb-md-4"
                    style={{
                      fontSize: "1.2rem",
                      lineHeight: "1.7",
                      color: "rgba(255, 255, 255, 0.9)",
                      textAlign: "justify",
                    }}
                  >
                    HR analytics does not just deal with gathering data on employee efficiency. Instead, it aims to provide insight into each process by gathering data and then using it to make relevant decisions about how to improve these processes.
                  </p>

                  <ul
                    className="list-unstyled mt-4"
                    style={{
                      fontSize: "1.2rem",
                      lineHeight: "1.8",
                      color: "rgba(255, 255, 255, 0.9)",
                    }}
                  >
                    {[
                      "Performance Analysis and optimization",
                      "Workforce Planning and forecasting",
                      // "Talent Development and training programs",
                      // "Retention Analysis and improvement strategies"
                    ].map((item, index) => (
                      <li
                        key={index}
                        className="mb-1 d-flex align-items-center" // Changed mb-3 to mb-1
                        style={{
                          padding: "0.8rem 0",
                        }}
                      >
                        <div
                          className="me-3 d-flex align-items-center justify-content-center"
                          style={{
                            width: "40px",
                            height: "40px",
                            borderRadius: "50%",
                            background: "rgba(0, 160, 233, 0.2)",
                            border: "1px solid rgba(0, 160, 233, 0.3)",
                          }}
                        >
                          <i
                            className="fas fa-user-tie"
                            style={{
                              color: "#00a0e9",
                              fontSize: "16px",
                            }}
                          ></i>
                        </div>
                        {item}
                      </li>
                    ))}
                  </ul>
                </div>
              </Col>
              <Col lg={6} md={10} className="pe-lg-4">
                <div
                  className="image-container"
                  style={{
                    borderRadius: "1.25rem",
                    overflow: "hidden",
                    boxShadow: "0 0.75rem 2rem rgba(0,0,0,0.1)",
                    transition: "transform 0.4s ease-out, box-shadow 0.4s ease-out",
                    backgroundColor: "#f0f2f5",
                  }}
                >
                  <img
                    src={Analytics4}
                    alt="HR Analytics and Workforce Management"
                    style={{
                      width: "100%",
                      height: "100%",
                      minHeight: "400px",
                      objectFit: "cover",
                      transition: "transform 0.6s ease",
                    }}
                  />
                </div>
              </Col>
            </Row>
          </section>
        </Container>
      </div>
      </div>
      <style>{`
        @keyframes float {
          0%, 100% {
            transform: translateY(0px);
          }
          50% {
            transform: translateY(-20px);
          }
        }
      `}</style>
    </div>
  );
};

export default AnalyticsPage;