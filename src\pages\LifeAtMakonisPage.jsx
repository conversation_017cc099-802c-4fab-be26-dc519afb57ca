import React, { useState, useEffect, useCallback } from "react";
import useScrollToTop from "../hooks/useScrollToTop";

// Import all Life@Makonis images
import image1 from "../assets/1.jpg";
import image3 from "../assets/3.jpg";
import image4 from "../assets/4.jpg";
import image5 from "../assets/5.jpg";
import image6 from "../assets/6.jpg";
import image7 from "../assets/7.jpg";
import image8 from "../assets/8.jpg";
import image9 from "../assets/9.jpg";
import image11 from "../assets/11.jpg";

import image13 from "../assets/13.jpg";
import image14 from "../assets/14.jpg";
import image15 from "../assets/15.jpg";
import image16 from "../assets/16.jpg";
import image18 from "../assets/18.jpg";
import image19 from "../assets/19.jpg";
import image20 from "../assets/20.jpg";
import image21 from "../assets/21.jpg";
import image22 from "../assets/22.jpg";
import image23 from "../assets/23.jpg";

import image25 from "../assets/25.jpg";
import image26 from "../assets/26.jpg";
import image28 from "../assets/28.jpg";
import image29 from "../assets/29.jpg";
import image30 from "../assets/30.jpeg";
import image31 from "../assets/31.jpeg";
import image32 from "../assets/32.jpeg";
import image33 from "../assets/33.png";
import image34 from "../assets/34.jpeg";

const imageData = [
  {
    src: image1,
    alt: "Makonis team working in the office",
  },
  {
    src: image3,
    alt: "Makonis office space",
  },
  {
    src: image4,
    alt: "Team collaboration at Makonis",
  },
  {
    src: image5,
    alt: "A celebratory moment at Makonis",
  },
  {
    src: image6,
    alt: "Makonis team event",
  },
  { src: image7, alt: "A project discussion" },
  { src: image8, alt: "Office amenities" },
  {
    src: image9,
    alt: "Team building activity",
  },
  {
    src: image11,
    alt: "Casual Friday at Makonis",
  },
  // {
  //   src: image12,
  //   alt: "Team bonding activity",
  // },
  {
    src: image13,
    alt: "Team bonding activity",
  },
  {
    src: image14,
    alt: "Team bonding activity",
  },
  {
    src: image15,
    alt: "Makonis team working in the office",
  },
  {
    src: image16,
    alt: "Makonis office space",
  },
  {
    src: image18,
    alt: "Team collaboration at Makonis",
  },
  {
    src: image19,
    alt: "A celebratory moment at Makonis",
  },
  {
    src: image20,
    alt: "Makonis team event",
  },
  { src: image21, alt: "A project discussion" },
  { src: image22, alt: "Office amenities" },
  {
    src: image23,
    alt: "Team building activity",
  },
  // {
  //   src: image24,
  //   alt: "Casual Friday at Makonis",
  // },
  {
    src: image25,
    alt: "Team bonding activity",
  },
  {
    src: image26,
    alt: "Team bonding activity",
  },
  {
    src: image28,
    alt: "Team bonding activity",
  },
  //  {
  //   src: "src/assets/Life@mako/27.mp4",
  //   alt: "Casual Friday at Makonis",
  // },
  // {
  //   src: "src/assets/Life@mako/17.mp4",
  //   alt: "Team bonding activity",
  // },yes

  // {
  //   src: "src/assets/Life@mako/10.mp4",
  //   alt: "Team bonding activity",
  // },
  // {
  //   src: "src/assets/Life@mako/2.mp4",
  //   alt: "Team bonding activity",
  // },
  {
    src: image29,
    alt: "Team bonding activity",
  },
  {
    src: image30,
    alt: "Team bonding activity",
  },
  {
    src: image31,
    alt: "Team bonding activity",
  },
  {
    src: image32,
    alt: "Team bonding activity",
  },
  {
    src: image33,
    alt: "Team bonding activity",
  },
  {
    src: image34,
    alt: "Team bonding activity",
  },

];

const LifeAtMakonisPage = () => {
  useScrollToTop();
  const [selectedImage, setSelectedImage] = useState(null);

  const handleNext = useCallback(() => {
    if (selectedImage !== null) {
      const currentIndex = imageData.findIndex(
        (img) => img.src === selectedImage
      );
      const nextIndex = (currentIndex + 1) % imageData.length;
      setSelectedImage(imageData[nextIndex].src);
    }
  }, [selectedImage]);

  const handlePrev = useCallback(() => {
    if (selectedImage !== null) {
      const currentIndex = imageData.findIndex(
        (img) => img.src === selectedImage
      );
      const prevIndex =
        (currentIndex - 1 + imageData.length) % imageData.length;
      setSelectedImage(imageData[prevIndex].src);
    }
  }, [selectedImage]);

  useEffect(() => {
    const handleKeyDown = (event) => {
      if (!selectedImage) return;
      if (event.key === "ArrowRight") handleNext();
      if (event.key === "ArrowLeft") handlePrev();
      if (event.key === "Escape") setSelectedImage(null);
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [selectedImage, handleNext, handlePrev]);

  const accentRgb = "0, 160, 233";
  const backgroundColor = "#001a35";

  return (
    <div
      style={{
        backgroundColor: backgroundColor,
        backgroundImage: `radial-gradient(circle at 30% 100%, rgba(${accentRgb}, 0.15) 0%, transparent 40%), radial-gradient(circle at 90% 20%, rgba(${accentRgb}, 0.1) 0%, transparent 30%)`,
      }}
    >
      {/* Header Section */}
      <div className="relative h-screen w-full flex items-center justify-center">
        <div
          className="absolute inset-0 bg-cover bg-center filter blur-sm"
          style={{
            backgroundImage:
              "url('https://images.unsplash.com/photo-1519389950473-47ba0277781c?fm=jpg&q=60&w=3000&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8c29mdHdhcmUlMjBkZXZlbG9wbWVudCUyMGNvbXBhbnl8ZW58MHx8MHx8fDA%3D')",
          }}
        ></div>
        <div className="absolute inset-0 bg-black opacity-50"></div>
        <div className="relative z-10 text-center">
         
          <h2  style={{
            fontSize: "3.6rem",
            fontWeight: "800",
            letterSpacing: "2.6px",
            marginBottom: "1rem",
            background: 'linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text',
            textShadow: '0 0 30px rgba(0, 160, 233, 0.3)'
          }}>
            Life @ Makonis
          </h2>
        </div>
      </div>

      {/* Grid Section */}
      <div className="mx-auto max-w-7xl px-4 py-16 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {imageData.map((image, index) => (
            <div
              key={`${image.src}-${index}`}
              className="overflow-hidden rounded-xl shadow-lg transition-transform duration-300 hover:scale-105 hover:shadow-2xl cursor-pointer"
              onClick={() => setSelectedImage(image.src)}
            >
              <img
                src={image.src}
                alt={image.alt}
                className="w-full h-60 object-cover"
              />
            </div>
          ))}
        </div>
      </div>

      {/* Full-screen Image Modal */}
      {selectedImage && (
        <div
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            backgroundColor: "black",
            zIndex: 9999,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            padding: "0 2rem",
            boxSizing: "border-box",
          }}
          onClick={() => setSelectedImage(null)}
        >
          <img
            src={selectedImage}
            alt="Full screen view"
            style={{
              maxWidth: "100%",
              maxHeight: "100%",
              objectFit: "contain",
            }}
            onClick={(e) => e.stopPropagation()}
          />
          <div
            onClick={(e) => {
              e.stopPropagation();
              setSelectedImage(null);
            }}
            style={{
              position: "absolute",
              top: "20px",
              right: "20px",
              width: "40px",
              height: "40px",
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              color: "white",
              borderRadius: "50%",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              fontSize: "24px",
              cursor: "pointer",
              zIndex: 10000,
            }}
          >
            X
          </div>
          <div
            onClick={(e) => {
              e.stopPropagation();
              handlePrev();
            }}
            style={{
              position: "absolute",
              left: "20px",
              top: "50%",
              transform: "translateY(-50%)",
              cursor: "pointer",
              zIndex: 10000,
              background: "rgba(0,0,0,0.5)",
              borderRadius: "50%",
              padding: "10px",
            }}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              style={{ height: "30px", width: "30px" }}
              fill="none"
              viewBox="0 0 24 24"
              stroke="white"
              strokeWidth={2}
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M15 19l-7-7 7-7"
              />
            </svg>
          </div>
          <div
            onClick={(e) => {
              e.stopPropagation();
              handleNext();
            }}
            style={{
              position: "absolute",
              right: "20px",
              top: "50%",
              transform: "translateY(-50%)",
              cursor: "pointer",
              zIndex: 10000,
              background: "rgba(0,0,0,0.5)",
              borderRadius: "50%",
              padding: "10px",
            }}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              style={{ height: "30px", width: "30px" }}
              fill="none"
              viewBox="0 0 24 24"
              stroke="white"
              strokeWidth={2}
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M9 5l7 7-7 7"
              />
            </svg>
          </div>
        </div>
      )}
    </div>
  );
};

export default LifeAtMakonisPage;
