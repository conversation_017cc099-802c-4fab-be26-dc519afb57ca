import React, { useEffect, useState, useMemo } from "react";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useLocation, useNavigate } from "react-router-dom";
import axios from "axios";
import Modal from "react-modal";
import "bootstrap/dist/css/bootstrap.min.css";
import {
  FaDownload,
  FaTimes,
  FaEye,
  FaPaperPlane,
  FaSpinner,
  FaUser,
  FaBuilding,
  FaMapMarkerAlt,
  FaClock,
  FaMoneyBillWave,
  FaCloudUploadAlt,
} from "react-icons/fa";

// Set the app element for react-modal accessibility
Modal.setAppElement("#root");

const Careers = () => {
  const [jobPosts, setJobPosts] = useState([]);
  const [activeFilter, setActiveFilter] = useState("India");

  const showSuccessToast = (message, containerId) => {
    toast.success(message, {
      containerId,
      position: "top-right",
      autoClose: 4000,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      className:
        "!bg-green-500 !text-white !rounded-xl !shadow-2xl !border-l-4 !border-green-400",
      bodyClassName: "!text-white !font-medium",
      progressClassName: "!bg-green-200",
      icon: "✅",
    });
  };

  const showErrorToast = (message, containerId) => {
    toast.error(message, {
      containerId,
      position: "top-right",
      autoClose: 6000,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      className:
        "!bg-red-500 !text-white !rounded-xl !shadow-2xl !border-l-4 !border-red-400",
      bodyClassName: "!text-white !font-medium",
      progressClassName: "!bg-red-200",
      icon: "❌",
    });
  };

  const showWarningToast = (message, containerId) => {
    toast.warn(message, {
      containerId,
      position: "top-right",
      autoClose: 5000,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      className:
        "!bg-amber-500 !text-white !rounded-xl !shadow-2xl !border-l-4 !border-amber-400",
      bodyClassName: "!text-white !font-medium",
      progressClassName: "!bg-amber-200",
      icon: "⚠️",
    });
  };

  const showInfoToast = (message, containerId) => {
    toast.info(message, {
      containerId,
      position: "top-right",
      autoClose: 4000,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      className:
        "!bg-blue-500 !text-white !rounded-xl !shadow-2xl !border-l-4 !border-blue-400",
      bodyClassName: "!text-white !font-medium",
      progressClassName: "!bg-blue-200",
      icon: "ℹ️",
    });
  };

  useEffect(() => {
    axios
      .get("http://192.168.0.48:5002/api/job_posts")
      .then((response) => {
        const transformedJobs = response.data.map((job, index) => {
          const {
            role,
            id,
            client,
            experience_min,
            experience_max,
            skills,
            location,
            mode,
            no_of_positions,
            shift_timings,
            jd_pdf_base64,
            jd_pdf_extension,
            jd_pdf_text,
            country,
          } = job;
          const jd_raw_clean =
            typeof jd_pdf_text === "string"
              ? jd_pdf_text.replace(/^b["']|["']$/g, "").replace(/\\n/g, "\n")
              : "";

          const lines = jd_raw_clean.split("\n");
          let jd_html = "";
          let in_list = false;

          lines.forEach((line) => {
            line = line.trim();
            if (!line) return;

            if (/:$/.test(line) || /^[A-Z\s]+$/.test(line)) {
              if (in_list) {
                jd_html += "</ul>";
                in_list = false;
              }
              jd_html += `<h3>${line}</h3>`;
            } else if (/^[•\-*\u2022]+\s*/u.test(line)) {
              if (!in_list) {
                jd_html += "<ul>";
                in_list = true;
              }
              const cleanLine = line.replace(/^[•\-*\u2022]+\s*/u, "");
              jd_html += `<li>${cleanLine}</li>`;
            } else {
              if (in_list) {
                jd_html += "</ul>";
                in_list = false;
              }
              jd_html += `<p>${line}</p>`;
            }
          });
          if (in_list) jd_html += "</ul>";
          return {
            jobtitle: role,
            jobid: id,
            client,
            min: experience_min,
            max: experience_max,
            skills,
            loc: location,
            mode,
            position: no_of_positions,
            qal: shift_timings,
            jd_pdf: jd_pdf_base64,
            jd_pdf_extension,
            jd_html,
            country: country,
            downloadFileName: `JD_${role}`,
            hasJD: !!jd_raw_clean.trim(),
          };
        });
        setJobPosts(transformedJobs);
      })
      .catch((error) => {
        console.error("Error fetching job posts:", error);
        showErrorToast("Failed to load job openings. Please refresh the page.");
      });
  }, []);

  const downloadJD = (base64, filename, extension) => {
    try {
      if (!base64 || !filename || !extension) {
        showErrorToast(
          "Download failed: Job description file is not available"
        );
        return;
      }

      const cleanBase64 = base64.replace(/^data:[^;]+;base64,/, "");
      const byteCharacters = atob(cleanBase64);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      const blob = new Blob([byteArray], {
        type:
          extension === "pdf" ? "application/pdf" : `application/${extension}`,
      });
      const url = URL.createObjectURL(blob);
      const element = document.createElement("a");
      element.href = url;
      element.download = `${filename}.${extension}`;
      document.body.appendChild(element);
      element.click();
      document.body.removeChild(element);
      URL.revokeObjectURL(url);

      setTimeout(() => {
        showSuccessToast(
          `Job description downloaded as ${filename}.${extension}`
        );
      }, 500);
    } catch (error) {
      console.error("Download error:", error);
      showErrorToast("Download failed: Unable to process the file.");
    }
  };

  const [selectedJob, setSelectedJob] = useState(null);
  const openModal = (job) => setSelectedJob(job);
  const closeModal = () => setSelectedJob(null);

  const [applyform, setApplyform] = useState(false);
  const [selectedJobId, setSelectedjobId] = useState("");
  const [selectedprofile, setSelectedprofile] = useState("");
  const [selectedclient, setSelectedclient] = useState("");

  const initialState = {
    job_id: "",
    name: "",
    mobile: "",
    email: "",
    client: "",
    skills: "",
    qualifications: "",
    reason_for_job_change: "",
    resume: null,
    current_company: "",
    position: "",
    current_job_location: "",
    preferred_job_location: "",
    total_experience_years: "0",
    total_experience_months: "0",
    relevant_experience_years: "0",
    relevant_experience_months: "0",
    current_ctc_type: "INR",
    current_ctc_value: "",
    expected_ctc_type: "INR",
    expected_ctc_value: "",
    serving_notice_period: "",
    holding_offer: "",
    period_of_notice: "",
    total_offers: "",
    linkedin: "",
    remarks: "",
    last_working_date: "",
    buyout: false,
    highest_package: "",
  };

  const [candidate_details, setDetails] = useState(initialState);
  const [selectedFile, setSelectedFile] = useState(null);
  const [waitForSubmission, setwaitForSubmission] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleCloseModal = () => {
    setApplyform(false);
    setDetails(initialState);
    setSelectedFile(null);
    setSelectedjobId(null);
    setSelectedprofile("");
    setSelectedclient("");
  };

  const handleOpenModal = (applyJob) => {
    setSelectedjobId(applyJob.jobid);
    setSelectedprofile(applyJob.jobtitle);
    setSelectedclient(applyJob.client);
    setApplyform(true);
  };

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    const val = type === "checkbox" ? checked : value;

    let updatedDetails = { ...candidate_details, [name]: val };

    if (name === "serving_notice_period") {
      updatedDetails = {
        ...updatedDetails,
        last_working_date: "",
        period_of_notice: "",
        buyout: false,
      };
    }
    if (name === "holding_offer" && value !== "yes") {
      updatedDetails = {
        ...updatedDetails,
        total_offers: "",
        highest_package: "",
      };
    }
    if (name === "qualifications") {
      updatedDetails.qualifications = value.replace(/[^a-zA-Z0-9 ]/g, "");
    }
    setDetails(updatedDetails);
  };

  const fileToBase64 = (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => resolve(reader.result.split(",")[1]);
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  };

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  const nameRegex = /^[A-Za-z\s]+$/;
  const isValidEmail = (email) => emailRegex.test(email);
  const isValidName = (name) => nameRegex.test(name);
  const isValidMobileNumber = (mobileNumber) => /^\d{10}$/.test(mobileNumber);

  const MODAL_TOAST_ID = "apply-form-toast";

  const confirmAddCandidate = async () => {
    try {
      if (!selectedFile) {
        showErrorToast("Resume file is required to proceed.", MODAL_TOAST_ID);
        setwaitForSubmission(false);
        return;
      }
      const base64String = await fileToBase64(selectedFile);
      const add_candidate_data = {
        ...candidate_details,
        job_id: selectedJobId,
        client: selectedclient,
        user_id: "51",
        from_page: "makoniscareer",
        profile: selectedprofile,
        resume: base64String,
        expected_ctc: `${candidate_details.expected_ctc_type} ${candidate_details.expected_ctc_value}`,
        current_ctc: `${candidate_details.current_ctc_type} ${candidate_details.current_ctc_value}`,
        experience: `${candidate_details.total_experience_years || 0}.${
          candidate_details.total_experience_months || 0
        }`,
        relevant_experience: `${
          candidate_details.relevant_experience_years || 0
        }.${candidate_details.relevant_experience_months || 0}`,
      };

      const response = await fetch("http://192.168.0.48:5002/add_candidate", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(add_candidate_data),
      });

      if (!response.ok)
        throw new Error(`HTTP error! status: ${response.status}`);
      const data = await response.json();

      if (data.status === "success" || data.status !== "error") {
        handleCloseModal();
        setTimeout(() => {
          const successMessage =
            data.message ||
            "Application submitted successfully! We'll be in touch.";
          showSuccessToast(successMessage);
        }, 300);
      } else {
        const errorMessage =
          data.message || data.error || "Unable to submit application.";
        showErrorToast(errorMessage, MODAL_TOAST_ID);
        if (data.details) console.error("API Error details:", data.details);
      }
    } catch (error) {
      console.error("Error in add_candidate:", error);
      showErrorToast(
        "An unexpected error occurred. Please try again.",
        MODAL_TOAST_ID
      );
    } finally {
      setwaitForSubmission(false);
    }
  };

  // --- MODIFICATION START: Updated handleSubmit function with full validation ---
  const handleSubmit = async (e) => {
    e.preventDefault();
    setwaitForSubmission(true);

    // Validation schema for required fields and their user-friendly names
    const requiredFields = {
      name: "Full Name",
      email: "Email",
      mobile: "Phone Number",
      skills: "Skills",
      qualifications: "Qualifications",
      current_company: "Current Company",
      position: "Current Position",
      current_job_location: "Current Job Location",
      preferred_job_location: "Preferred Job Location",
      total_experience_years: "Total Experience (Years)",
      total_experience_months: "Total Experience (Months)",
      relevant_experience_years: "Relevant Experience (Years)",
      relevant_experience_months: "Relevant Experience (Months)",
      current_ctc_value: "Current CTC",
      expected_ctc_value: "Expected CTC",
      serving_notice_period: "Serving Notice Period status",
      holding_offer: "Holding Offer status",
    };

    // Check standard required fields
    for (const [field, name] of Object.entries(requiredFields)) {
      if (
        !candidate_details[field] ||
        !String(candidate_details[field]).trim()
      ) {
        showWarningToast(`${name} is required.`, MODAL_TOAST_ID);
        setwaitForSubmission(false);
        return;
      }
    }

    // Specific format validations
    if (!isValidName(candidate_details.name)) {
      showWarningToast("A valid name is required.", MODAL_TOAST_ID);
      setwaitForSubmission(false);
      return;
    }
    if (!isValidEmail(candidate_details.email)) {
      showWarningToast("A valid email is required.", MODAL_TOAST_ID);
      setwaitForSubmission(false);
      return;
    }
    if (!isValidMobileNumber(candidate_details.mobile)) {
      showWarningToast(
        "A valid 10-digit phone number is required.",
        MODAL_TOAST_ID
      );
      setwaitForSubmission(false);
      return;
    }

    // Conditional validations
    if (
      candidate_details.serving_notice_period === "yes" &&
      !candidate_details.last_working_date
    ) {
      showWarningToast("Last Working Date is required.", MODAL_TOAST_ID);
      setwaitForSubmission(false);
      return;
    }
    if (
      candidate_details.serving_notice_period === "no" &&
      !candidate_details.period_of_notice
    ) {
      showWarningToast("Notice Period is required.", MODAL_TOAST_ID);
      setwaitForSubmission(false);
      return;
    }
    if (candidate_details.holding_offer === "yes") {
      if (!candidate_details.total_offers) {
        showWarningToast("Total number of offers is required.", MODAL_TOAST_ID);
        setwaitForSubmission(false);
        return;
      }
      if (!candidate_details.highest_package) {
        showWarningToast("Highest package is required.", MODAL_TOAST_ID);
        setwaitForSubmission(false);
        return;
      }
    }

    // Check for resume file
    if (!selectedFile) {
      showWarningToast("Please upload your resume.", MODAL_TOAST_ID);
      setwaitForSubmission(false);
      return;
    }

    const candidate_data = {
      mobile: candidate_details.mobile.trim(),
      email: candidate_details.email.trim(),
      job_id: selectedJobId,
    };

    try {
      const response = await fetch(
        "http://192.168.0.48:5002/check_careers_candidate",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(candidate_data),
        }
      );

      if (!response.ok)
        throw new Error(`HTTP error! status: ${response.status}`);
      const data = await response.json();

      if (data.exists === true) {
        showErrorToast(
          data.message || "You have already applied for this role.",
          MODAL_TOAST_ID
        );
        setwaitForSubmission(false);
        return;
      } else if (data.exists === false) {
        await confirmAddCandidate();
      } else {
        showErrorToast(
          "Unexpected response from server. Please try again.",
          MODAL_TOAST_ID
        );
        setwaitForSubmission(false);
      }
    } catch (error) {
      console.error("Error in check_careers_candidate:", error);
      showErrorToast(
        "Could not verify candidate details. Please try again.",
        MODAL_TOAST_ID
      );
      setwaitForSubmission(false);
    }
  };
  // --- MODIFICATION END ---

  const handleSubmitResume = async (e) => {
    e.preventDefault();
    const file = e.target.files[0];

    if (!file) return;
    setSelectedFile(file);

    const allowedTypes = [".pdf", ".doc", ".docx"];
    const fileExtension = file.name
      .toLowerCase()
      .substring(file.name.lastIndexOf("."));
    if (!allowedTypes.includes(fileExtension) || file.size > 50 * 1024 * 1024) {
      showErrorToast(
        "Invalid file. Use PDF, DOC, or DOCX under 50MB.",
        MODAL_TOAST_ID
      );
      return;
    }

    setIsLoading(true);

    try {
      const base64String = await fileToBase64(file);
      const response = await fetch("http://192.168.0.48:5002/parse_resume", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ resume: base64String }),
      });

      if (response.ok) {
        const data = await response.json();
        const {
          name,
          mail,
          skill1,
          phone,
          current_company,
          position,
          current_job_location,
          qualifications,
        } = data;

        if (!Object.values(data).some((v) => !!v)) {
          showWarningToast(
            "Could not extract data. Please fill the form manually.",
            MODAL_TOAST_ID
          );
          return;
        }

        setDetails((prev) => ({
          ...prev,
          name: name || "",
          email: mail || "",
          skills: skill1 || "",
          mobile: phone || "",
          current_company: current_company || "",
          position: position || "",
          current_job_location: current_job_location || "",
          qualifications: qualifications || "",
        }));

        const extractedFields = [
          name && "Name",
          mail && "Email",
          phone && "Phone",
          skill1 && "Skills",
          current_company && "Company",
          position && "Position",
        ].filter(Boolean);
        const successMsg = `Parsed resume! We filled in: ${extractedFields.join(
          ", "
        )}. Please review.`;
        showSuccessToast(
          extractedFields.length
            ? successMsg
            : "Resume uploaded. Please complete the form.",
          MODAL_TOAST_ID
        );
      } else {
        const errorData = await response.json();
        showErrorToast(
          `Parsing failed: ${errorData.message || "Unknown error"}.`,
          MODAL_TOAST_ID
        );
      }
    } catch (err) {
      console.error("Resume parsing error:", err);
      showErrorToast(
        "Resume parsing failed. Please fill out the form manually.",
        MODAL_TOAST_ID
      );
    } finally {
      setIsLoading(false);
    }
  };

  const filteredJobs = useMemo(() => {
    return jobPosts.filter((job) => job.country === activeFilter);
  }, [jobPosts, activeFilter]);

  return (
    <section className="min-h-screen flex items-center relative overflow-hidden py-20 bg-gradient-to-br from-makonis-primary/95 to-makonis-primary/98 backdrop-blur-sm">
      <div className="absolute inset-0 z-0">
        <div
          className="absolute inset-0 opacity-20"
          style={{
            backgroundImage: `linear-gradient(rgba(0, 160, 233, 0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(0, 160, 233, 0.1) 1px, transparent 1px)`,
            backgroundSize: "50px 50px",
          }}
        />
      </div>
      <div className="relative z-10 w-full">
        <div className="container mx-auto px-4">
          <div className="mb-10">
            <div className="text-center">
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-extrabold mb-3 tracking-wider pb-2 bg-gradient-to-r from-white to-makonis-secondary bg-clip-text text-transparent drop-shadow-lg">
                Current Openings
              </h1>
            </div>
          </div>

          <div className="mb-10">
            <div className="flex justify-center">
              <div className="inline-flex items-center p-1.5 bg-makonis-primary/70 rounded-full border border-white/10 backdrop-blur-sm shadow-lg">
                <button
                  type="button"
                  className={`px-6 py-2.5 rounded-full font-semibold text-sm transition-all duration-300 whitespace-nowrap ${
                    activeFilter === "India"
                      ? "bg-gradient-to-r from-makonis-secondary to-blue-500 text-white shadow-lg shadow-makonis-secondary/30 scale-105 transform"
                      : "text-gray-200 hover:bg-white/10 hover:text-white hover:scale-105 transform"
                  }`}
                  onClick={() => setActiveFilter("India")}
                >
                  India Openings
                </button>
                <button
                  type="button"
                  className={`px-6 py-2.5 rounded-full font-semibold text-sm transition-all duration-300 whitespace-nowrap ${
                    activeFilter === "USA"
                      ? "bg-gradient-to-r from-makonis-secondary to-blue-500 text-white shadow-lg shadow-makonis-secondary/30 scale-105 transform"
                      : "text-gray-200 hover:bg-white/10 hover:text-white hover:scale-105 transform"
                  }`}
                  onClick={() => setActiveFilter("USA")}
                >
                  US Openings
                </button>
              </div>
            </div>
          </div>

          <div className="flex justify-center">
            <div className="w-full max-w-5xl space-y-6">
              {jobPosts.length > 0 ? (
                filteredJobs.length > 0 ? (
                  filteredJobs.map((job) => (
                    <div key={job.jobid} className="w-full">
                      <div className="bg-white/95 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-100 transition-all duration-300 hover:-translate-y-1 hover:shadow-xl hover:shadow-black/10 hover:bg-white">
                        <div className="p-6">
                          <h4 className="text-2xl font-bold mb-4 text-makonis-primary">
                            {job.jobtitle}
                          </h4>

                          <div className="flex flex-col items-start gap-3 mb-6">
                            <div className="flex flex-wrap items-center gap-2">
                              <span className="inline-flex items-center px-3 py-1.5 bg-gray-50 text-gray-700 border border-gray-200 rounded-lg text-sm font-medium hover:bg-gray-100 transition-colors">
                                <span className="font-semibold mr-1">
                                  Experience:
                                </span>{" "}
                                {job.min} - {job.max} years
                              </span>
                              <span className="inline-flex items-center px-3 py-1.5 bg-blue-50 text-blue-700 border border-blue-200 rounded-lg text-sm font-medium hover:bg-blue-100 transition-colors">
                                <FaMapMarkerAlt className="mr-1" size={12} />
                                {job.loc}
                              </span>
                              <span className="inline-flex items-center px-3 py-1.5 bg-green-50 text-green-700 border border-green-200 rounded-lg text-sm font-medium hover:bg-green-100 transition-colors">
                                <span className="font-semibold mr-1">
                                  Mode:
                                </span>{" "}
                                {job.mode}
                              </span>
                            </div>
                            {job.skills && (
                              <div className="flex flex-wrap items-center gap-2">
                                <span className="text-sm font-semibold text-gray-700 mr-2">
                                  Skills:
                                </span>
                                {job.skills.split(",").map(
                                  (skill, index) =>
                                    skill.trim() && (
                                      <span
                                        key={index}
                                        className="inline-flex items-center px-2.5 py-1 bg-purple-50 text-purple-700 border border-purple-200 rounded-md text-xs font-medium hover:bg-purple-100 transition-colors"
                                      >
                                        {skill.trim()}
                                      </span>
                                    )
                                )}
                              </div>
                            )}
                          </div>

                          <div className="flex justify-between items-center mt-6 gap-3">
                            <button
                              className="flex items-center justify-center gap-2 px-5 py-2.5 border-2 border-blue-500 text-blue-500 font-semibold rounded-lg hover:bg-blue-50 hover:border-blue-600 hover:text-blue-600 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
                              onClick={() => openModal(job)}
                            >
                              <FaEye size={16} />
                              <span>View JD</span>
                            </button>
                            <button
                              className="flex items-center justify-center gap-2 px-5 py-2.5 bg-gradient-to-r from-makonis-primary to-makonis-secondary text-white font-semibold rounded-lg hover:shadow-lg hover:shadow-makonis-secondary/30 hover:scale-105 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-makonis-secondary focus:ring-opacity-50"
                              onClick={() => handleOpenModal(job)}
                            >
                              <FaPaperPlane size={14} />
                              <span>Apply Now</span>
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center text-white/70 py-20">
                    <div className="bg-white/10 backdrop-blur-sm rounded-xl p-8 max-w-md mx-auto">
                      <p className="text-xl mb-2">No current openings found</p>
                      <p className="text-sm opacity-75">
                        Check back soon for new opportunities in this region.
                      </p>
                    </div>
                  </div>
                )
              ) : (
                <div className="text-center text-white/70 py-20">
                  <div className="bg-white/10 backdrop-blur-sm rounded-xl p-8 max-w-md mx-auto">
                    <FaSpinner className="animate-spin mx-auto mb-4 text-4xl" />
                    <p className="text-xl">Loading available positions...</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
        <Modal
          isOpen={!!selectedJob}
          onRequestClose={closeModal}
          contentLabel="Job Description Modal"
          bodyOpenClassName="body-modal-open"
          style={{
            overlay: {
              backgroundColor: "rgba(0, 0, 0, 0.7)",
              backdropFilter: "blur(5px)",
              zIndex: 9998,
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            },
            content: {
              position: "relative",
              top: "auto",
              left: "auto",
              right: "auto",
              bottom: "auto",
              width: window.innerWidth <= 768 ? "95%" : "70%",
              maxWidth: "900px",
              maxHeight: "85vh",
              overflow: "hidden",
              margin: "auto",
              background: "white",
              borderRadius: "20px",
              border: "none",
              padding: "0",
              boxShadow: "0 20px 60px rgba(0, 0, 0, 0.3)",
            },
          }}
        >
          {selectedJob && (
            <div className="h-100 d-flex flex-column">
              <div className="flex justify-between items-center px-6 py-4 bg-makonis-primary rounded-t-xl border-b border-gray-200">
                <h2 className="text-white text-xl font-bold">
                  Job Description
                </h2>
                <div className="flex items-center gap-3">
                  <button
                    className="flex items-center gap-2 px-4 py-2 bg-white text-makonis-primary font-medium rounded-lg hover:bg-gray-50 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50"
                    onClick={() =>
                      downloadJD(
                        selectedJob.jd_pdf,
                        selectedJob.downloadFileName,
                        selectedJob.jd_pdf_extension
                      )
                    }
                  >
                    <FaDownload size={14} />
                    Download
                  </button>
                  <button
                    className="flex items-center justify-center w-10 h-10 bg-white text-makonis-primary rounded-lg hover:bg-gray-50 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50"
                    onClick={closeModal}
                  >
                    <FaTimes size={16} />
                  </button>
                </div>
              </div>

              <div
                className="flex-grow-1 p-4"
                style={{ overflowY: "auto", maxHeight: "calc(85vh - 74px)" }}
              >
                <div
                  className="job-description-content"
                  dangerouslySetInnerHTML={{ __html: selectedJob.jd_html }}
                />
                <style jsx>{`
                  .job-description-content h3 {
                    font-weight: 600 !important;
                    font-size: 1.2rem !important;
                    margin: 1.5rem 0 0.8rem 0 !important;
                    border-bottom: 2px solid #00a0e9 !important;
                    padding-bottom: 0.5rem !important;
                  }
                  .job-description-content ul {
                    padding-left: 1.5rem !important;
                    margin-bottom: 1rem !important;
                  }
                  .job-description-content li {
                    margin-bottom: 0.5rem !important;
                    line-height: 1.5 !important;
                  }
                  .job-description-content p {
                    margin: 0.8rem 0 !important;
                    line-height: 1.6 !important;
                  }
                `}</style>
              </div>
            </div>
          )}
        </Modal>

        <Modal
          isOpen={applyform}
          onRequestClose={handleCloseModal}
          contentLabel="Application Form Modal"
          bodyOpenClassName="body-modal-open"
          style={{
            overlay: {
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              backdropFilter: "blur(5px)",
              zIndex: 9999,
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            },
            content: {
              position: "relative",
              top: "auto",
              left: "auto",
              right: "auto",
              bottom: "auto",
              width: window.innerWidth <= 768 ? "95%" : "95%",
              maxWidth: "1200px",
              maxHeight: "95vh",
              overflow: "hidden",
              margin: "auto",
              background: "white",
              borderRadius: "12px",
              border: "none",
              padding: "0",
              boxShadow: "0 25px 80px rgba(0, 0, 0, 0.4)",
            },
          }}
        >
          <div className="h-100 d-flex flex-column">
            <div
              className="d-flex justify-content-between align-items-center px-4 py-3"
              style={{ background: "#002956", borderRadius: "12px 12px 0 0" }}
            >
              <h2
                className="text-white mb-0 fw-bold"
                style={{ fontSize: "1.5rem" }}
              >
                Profile Submission
              </h2>
              <button
                className="btn btn-light btn-sm p-2"
                onClick={handleCloseModal}
                style={{
                  borderRadius: "8px",
                  width: "36px",
                  height: "36px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <FaTimes size={14} />
              </button>
            </div>

            <ToastContainer
              enableMultiContainer
              containerId={MODAL_TOAST_ID}
              position="top-right"
              autoClose={5000}
              hideProgressBar={false}
              newestOnTop
              closeOnClick
              rtl={false}
              pauseOnFocusLoss
              draggable
              pauseOnHover
              theme="colored"
              style={{ zIndex: 99999 }}
            />

            <div
              className="flex-grow-1"
              style={{ height: "calc(95vh - 72px)", overflow: "hidden" }}
            >
              <form
                className="needs-validation h-100"
                noValidate
                onSubmit={handleSubmit}
              >
                <div className="row g-0 h-100">
                  <div
                    className="col-lg-8 p-4"
                    style={{ overflowY: "auto", height: "100%" }}
                  >
                    <div className="mb-4">
                      <h6
                        className="fw-bold mb-3 d-flex align-items-center gap-2"
                        style={{ color: "#007bff", fontSize: "16px" }}
                      >
                        <FaUser size={16} />
                        Personal Information
                      </h6>

                      <div className="row g-3">
                        <div className="col-md-6">
                          <label
                            className="form-label fw-medium"
                            style={{
                              color: "#333",
                              fontSize: "14px",
                            }}
                          >
                            Full Name *
                          </label>
                          <input
                            type="text"
                            className="form-control focus:border-makonis-primary focus:ring-2 focus:ring-makonis-primary/25 transition-all duration-300"
                            name="name"
                            placeholder="Your Full Name"
                            value={candidate_details.name}
                            onChange={handleChange}
                            required
                            style={{
                              borderRadius: "6px",
                              border: "1px solid #ddd",
                              padding: "10px 12px",
                              fontSize: "14px",
                            }}
                          />
                        </div>

                        <div className="col-md-6">
                          <label
                            className="form-label fw-medium"
                            style={{
                              color: "#333",
                              fontSize: "14px",
                            }}
                          >
                            Email *
                          </label>
                          <input
                            type="email"
                            className="form-control focus:border-makonis-primary focus:ring-2 focus:ring-makonis-primary/25 transition-all duration-300"
                            name="email"
                            placeholder="Your Email"
                            value={candidate_details.email}
                            onChange={handleChange}
                            required
                            style={{
                              borderRadius: "6px",
                              border: "1px solid #ddd",
                              padding: "10px 12px",
                              fontSize: "14px",
                            }}
                          />
                        </div>

                        <div className="col-md-6">
                          <label
                            className="form-label fw-medium"
                            style={{
                              color: "#333",
                              fontSize: "14px",
                            }}
                          >
                            Phone Number *
                          </label>
                          <input
                            type="tel"
                            className="form-control focus:border-makonis-primary focus:ring-2 focus:ring-makonis-primary/25 transition-all duration-300"
                            name="mobile"
                            placeholder="Your Phone Number"
                            value={candidate_details.mobile}
                            onChange={handleChange}
                            required
                            style={{
                              borderRadius: "6px",
                              border: "1px solid #ddd",
                              padding: "10px 12px",
                              fontSize: "14px",
                            }}
                          />
                        </div>

                        <div className="col-md-6">
                          <label
                            className="form-label fw-medium"
                            style={{
                              color: "#333",
                              fontSize: "14px",
                            }}
                          >
                            Skills *
                          </label>
                          <input
                            type="text"
                            className="form-control focus:border-makonis-primary focus:ring-2 focus:ring-makonis-primary/25 transition-all duration-300"
                            name="skills"
                            placeholder="Your Skills"
                            value={candidate_details.skills}
                            onChange={handleChange}
                            required
                            style={{
                              borderRadius: "6px",
                              border: "1px solid #ddd",
                              padding: "10px 12px",
                              fontSize: "14px",
                            }}
                          />
                        </div>

                        <div className="col-md-12">
                          <label
                            className="form-label fw-medium"
                            style={{
                              color: "#333",
                              fontSize: "14px",
                            }}
                          >
                            Qualifications *
                          </label>
                          <input
                            type="text"
                            className="form-control focus:border-makonis-primary focus:ring-2 focus:ring-makonis-primary/25 transition-all duration-300"
                            name="qualifications"
                            placeholder="Your Qualifications"
                            value={candidate_details.qualifications}
                            onChange={handleChange}
                            required
                            style={{
                              borderRadius: "6px",
                              border: "1px solid #ddd",
                              padding: "10px 12px",
                              fontSize: "14px",
                            }}
                          />
                        </div>
                      </div>
                    </div>

                    <div className="mb-4">
                      <h6
                        className="fw-bold mb-3 d-flex align-items-center gap-2"
                        style={{ color: "#007bff", fontSize: "16px" }}
                      >
                        <FaBuilding size={16} />
                        Professional Information
                      </h6>

                      <div className="row g-3">
                        <div className="col-md-6">
                          <label
                            className="form-label fw-medium"
                            style={{
                              color: "#333",
                              fontSize: "14px",
                            }}
                          >
                            Current Company *
                          </label>
                          <input
                            type="text"
                            className="form-control focus:border-makonis-primary focus:ring-2 focus:ring-makonis-primary/25 transition-all duration-300"
                            name="current_company"
                            placeholder="Your Current Company"
                            value={candidate_details.current_company}
                            onChange={handleChange}
                            required
                            style={{
                              borderRadius: "6px",
                              border: "1px solid #ddd",
                              padding: "10px 12px",
                              fontSize: "14px",
                            }}
                          />
                        </div>

                        <div className="col-md-6">
                          <label
                            className="form-label fw-medium"
                            style={{
                              color: "#333",
                              fontSize: "14px",
                            }}
                          >
                            Current Position *
                          </label>
                          <input
                            type="text"
                            className="form-control focus:border-makonis-primary focus:ring-2 focus:ring-makonis-primary/25 transition-all duration-300"
                            name="position"
                            placeholder="Your Current Position"
                            value={candidate_details.position}
                            onChange={handleChange}
                            required
                            style={{
                              borderRadius: "6px",
                              border: "1px solid #ddd",
                              padding: "10px 12px",
                              fontSize: "14px",
                            }}
                          />
                        </div>

                        <div className="col-md-6">
                          <label
                            className="form-label fw-medium"
                            style={{
                              color: "#333",
                              fontSize: "14px",
                            }}
                          >
                            Job Location *
                          </label>
                          <input
                            type="text"
                            className="form-control focus:border-makonis-primary focus:ring-2 focus:ring-makonis-primary/25 transition-all duration-300"
                            name="current_job_location"
                            placeholder="Your Job Location"
                            value={candidate_details.current_job_location}
                            onChange={handleChange}
                            required
                            style={{
                              borderRadius: "6px",
                              border: "1px solid #ddd",
                              padding: "10px 12px",
                              fontSize: "14px",
                            }}
                          />
                        </div>

                        <div className="col-md-6">
                          <label
                            className="form-label fw-medium"
                            style={{
                              color: "#333",
                              fontSize: "14px",
                            }}
                          >
                            Preferred Job Location *
                          </label>
                          <input
                            type="text"
                            className="form-control focus:border-makonis-primary focus:ring-2 focus:ring-makonis-primary/25 transition-all duration-300"
                            name="preferred_job_location"
                            placeholder="Your Preferred Job Location"
                            value={candidate_details.preferred_job_location}
                            onChange={handleChange}
                            required
                            style={{
                              borderRadius: "6px",
                              border: "1px solid #ddd",
                              padding: "10px 12px",
                              fontSize: "14px",
                            }}
                          />
                        </div>
                      </div>
                    </div>

                    <div className="col-12 mb-4">
                      <div className="form-floating">
                        <textarea
                          className="form-control focus:border-makonis-primary focus:ring-2 focus:ring-makonis-primary/25 transition-all duration-300"
                          id="reason_for_job_change"
                          name="reason_for_job_change"
                          placeholder="Reason for Job Change"
                          value={candidate_details.reason_for_job_change}
                          onChange={handleChange}
                          style={{
                            borderRadius: "10px",
                            minHeight: "100px",
                          }}
                        />
                        <label htmlFor="reason_for_job_change">
                          Reason for Job Change
                        </label>
                      </div>
                    </div>

                    <div className="mb-4">
                      <h6
                        className="fw-bold mb-3 d-flex align-items-center gap-2"
                        style={{ color: "#007bff", fontSize: "16px" }}
                      >
                        <FaClock size={16} />
                        Experience Details
                      </h6>

                      <div className="row g-3">
                        <div className="col-md-6">
                          <label
                            className="form-label fw-medium"
                            style={{
                              color: "#333",
                              fontSize: "14px",
                            }}
                          >
                            Total Experience *
                          </label>
                          <div className="row g-2">
                            <div className="col-6">
                              <select
                                className="form-select focus:border-makonis-primary focus:ring-2 focus:ring-makonis-primary/25 transition-all duration-300"
                                name="total_experience_years"
                                value={candidate_details.total_experience_years}
                                onChange={handleChange}
                                required
                                style={{
                                  borderRadius: "6px",
                                  border: "1px solid #ddd",
                                  padding: "10px 12px",
                                  fontSize: "14px",
                                }}
                              >
                                <option value="">Years</option>
                                {Array.from({ length: 21 }, (_, i) => (
                                  <option key={i} value={i}>
                                    {i}
                                  </option>
                                ))}
                              </select>
                            </div>
                            <div className="col-6">
                              <select
                                className="form-select focus:border-makonis-primary focus:ring-2 focus:ring-makonis-primary/25 transition-all duration-300"
                                name="total_experience_months"
                                value={
                                  candidate_details.total_experience_months
                                }
                                onChange={handleChange}
                                required
                                style={{
                                  borderRadius: "6px",
                                  border: "1px solid #ddd",
                                  padding: "10px 12px",
                                  fontSize: "14px",
                                }}
                              >
                                <option value="">Months</option>
                                {Array.from({ length: 12 }, (_, i) => (
                                  <option key={i} value={i}>
                                    {i}
                                  </option>
                                ))}
                              </select>
                            </div>
                          </div>
                        </div>

                        <div className="col-md-6">
                          <label
                            className="form-label fw-medium"
                            style={{
                              color: "#333",
                              fontSize: "14px",
                            }}
                          >
                            Relevant Experience *
                          </label>
                          <div className="row g-2">
                            <div className="col-6">
                              <select
                                className="form-select focus:border-makonis-primary focus:ring-2 focus:ring-makonis-primary/25 transition-all duration-300"
                                name="relevant_experience_years"
                                value={
                                  candidate_details.relevant_experience_years
                                }
                                onChange={handleChange}
                                required
                                style={{
                                  borderRadius: "6px",
                                  border: "1px solid #ddd",
                                  padding: "10px 12px",
                                  fontSize: "14px",
                                }}
                              >
                                <option value="">Years</option>
                                {Array.from({ length: 21 }, (_, i) => (
                                  <option key={i} value={i}>
                                    {i}
                                  </option>
                                ))}
                              </select>
                            </div>
                            <div className="col-6">
                              <select
                                className="form-select focus:border-makonis-primary focus:ring-2 focus:ring-makonis-primary/25 transition-all duration-300"
                                name="relevant_experience_months"
                                value={
                                  candidate_details.relevant_experience_months
                                }
                                onChange={handleChange}
                                required
                                style={{
                                  borderRadius: "6px",
                                  border: "1px solid #ddd",
                                  padding: "10px 12px",
                                  fontSize: "14px",
                                }}
                              >
                                <option value="">Months</option>
                                {Array.from({ length: 12 }, (_, i) => (
                                  <option key={i} value={i}>
                                    {i}
                                  </option>
                                ))}
                              </select>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="mb-4">
                      <h6
                        className="fw-bold mb-3 d-flex align-items-center gap-2"
                        style={{ color: "#007bff", fontSize: "16px" }}
                      >
                        <FaMoneyBillWave size={16} />
                        Compensation Details
                      </h6>

                      <div className="row g-3">
                        <div className="col-md-6">
                          <label
                            className="form-label fw-medium"
                            style={{
                              color: "#333",
                              fontSize: "14px",
                            }}
                          >
                            Current CTC *
                          </label>
                          <div className="row g-2">
                            <div className="col-4">
                              <select
                                className="form-select focus:border-makonis-primary focus:ring-2 focus:ring-makonis-primary/25 transition-all duration-300"
                                name="current_ctc_type"
                                value={candidate_details.current_ctc_type}
                                onChange={handleChange}
                                required
                                style={{
                                  borderRadius: "6px",
                                  border: "1px solid #ddd",
                                  padding: "10px 12px",
                                  fontSize: "14px",
                                }}
                              >
                                <option value="INR">INR (LPA)</option>
                                <option value="USD">USD</option>
                                <option value="EUR">EUR</option>
                                <option value="CAD">CAD</option>
                              </select>
                            </div>
                            <div className="col-8">
                              <input
                                type="number"
                                className="form-control focus:border-makonis-primary focus:ring-2 focus:ring-makonis-primary/25 transition-all duration-300"
                                name="current_ctc_value"
                                placeholder="Enter Amount"
                                value={candidate_details.current_ctc_value}
                                onChange={handleChange}
                                required
                                style={{
                                  borderRadius: "6px",
                                  border: "1px solid #ddd",
                                  padding: "10px 12px",
                                  fontSize: "14px",
                                }}
                              />
                            </div>
                          </div>
                        </div>

                        <div className="col-md-6">
                          <label
                            className="form-label fw-medium"
                            style={{
                              color: "#333",
                              fontSize: "14px",
                            }}
                          >
                            Expected CTC *
                          </label>
                          <div className="row g-2">
                            <div className="col-4">
                              <select
                                className="form-select focus:border-makonis-primary focus:ring-2 focus:ring-makonis-primary/25 transition-all duration-300"
                                name="expected_ctc_type"
                                value={candidate_details.expected_ctc_type}
                                onChange={handleChange}
                                required
                                style={{
                                  borderRadius: "6px",
                                  border: "1px solid #ddd",
                                  padding: "10px 12px",
                                  fontSize: "14px",
                                }}
                              >
                                <option value="INR">INR (LPA)</option>
                                <option value="USD">USD</option>
                                <option value="EUR">EUR</option>
                                <option value="CAD">CAD</option>
                              </select>
                            </div>
                            <div className="col-8">
                              <input
                                type="number"
                                className="form-control focus:border-makonis-primary focus:ring-2 focus:ring-makonis-primary/25 transition-all duration-300"
                                name="expected_ctc_value"
                                placeholder="Enter Amount"
                                value={candidate_details.expected_ctc_value}
                                onChange={handleChange}
                                required
                                style={{
                                  borderRadius: "6px",
                                  border: "1px solid #ddd",
                                  padding: "10px 12px",
                                  fontSize: "14px",
                                }}
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="mb-4">
                      <h6
                        className="fw-bold mb-3 d-flex align-items-center gap-2"
                        style={{ color: "#007bff", fontSize: "16px" }}
                      >
                        <FaClock size={16} />
                        Notice Period & Offer Details
                      </h6>
                      <div className="row g-3">
                        <div className="col-md-6">
                          <label
                            className="form-label fw-medium"
                            style={{
                              color: "#333",
                              fontSize: "14px",
                            }}
                          >
                            Serving Notice Period *
                          </label>
                          <select
                            className="form-select focus:border-makonis-primary focus:ring-2 focus:ring-makonis-primary/25 transition-all duration-300"
                            name="serving_notice_period"
                            value={candidate_details.serving_notice_period}
                            onChange={handleChange}
                            required
                            style={{
                              borderRadius: "6px",
                              border: "1px solid #ddd",
                              padding: "10px 12px",
                              fontSize: "14px",
                            }}
                          >
                            <option value="">Select</option>
                            <option value="yes">Yes</option>
                            <option value="no">No</option>
                            <option value="completed">Completed</option>
                          </select>
                        </div>

                        <div className="col-md-6">
                          <label
                            className="form-label fw-medium"
                            style={{
                              color: "#333",
                              fontSize: "14px",
                            }}
                          >
                            Holding Offer *
                          </label>
                          <select
                            className="form-select focus:border-makonis-primary focus:ring-2 focus:ring-makonis-primary/25 transition-all duration-300"
                            name="holding_offer"
                            value={candidate_details.holding_offer}
                            onChange={handleChange}
                            required
                            style={{
                              borderRadius: "6px",
                              border: "1px solid #ddd",
                              padding: "10px 12px",
                              fontSize: "14px",
                            }}
                          >
                            <option value="">Select</option>
                            <option value="yes">Yes</option>
                            <option value="no">No</option>
                            <option value="pipeline">Pipeline</option>
                          </select>
                        </div>

                        {candidate_details.serving_notice_period === "yes" && (
                          <>
                            <div className="col-md-6">
                              <label
                                className="form-label fw-medium"
                                style={{
                                  color: "#333",
                                  fontSize: "14px",
                                }}
                              >
                                Last Working Date *
                              </label>
                              <input
                                type="date"
                                name="last_working_date"
                                className="form-control focus:border-makonis-primary focus:ring-2 focus:ring-makonis-primary/25 transition-all duration-300"
                                value={candidate_details.last_working_date}
                                onChange={handleChange}
                                required
                                style={{
                                  borderRadius: "6px",
                                  border: "1px solid #ddd",
                                  padding: "10px 12px",
                                  fontSize: "14px",
                                }}
                              />
                            </div>
                            <div className="col-md-6 d-flex align-items-end pb-2 text-dark">
                              <div className="form-check">
                                <input
                                  className="form-check-input"
                                  type="checkbox"
                                  name="buyout"
                                  id="buyoutCheck1"
                                  checked={candidate_details.buyout}
                                  onChange={handleChange}
                                />
                                <label
                                  className="form-check-label"
                                  htmlFor="buyoutCheck1"
                                  style={{ color: "#353935" }}
                                >
                                  Buyout
                                </label>
                              </div>
                            </div>
                          </>
                        )}

                        {candidate_details.serving_notice_period === "no" && (
                          <>
                            <div className="col-md-6">
                              <label
                                className="form-label fw-medium"
                                style={{
                                  color: "#333",
                                  fontSize: "14px",
                                }}
                              >
                                Notice Period *
                              </label>
                              <select
                                className="form-select focus:border-makonis-primary focus:ring-2 focus:ring-makonis-primary/25 transition-all duration-300"
                                name="period_of_notice"
                                value={candidate_details.period_of_notice}
                                onChange={handleChange}
                                required
                                style={{
                                  borderRadius: "6px",
                                  border: "1px solid #ddd",
                                  padding: "10px 12px",
                                  fontSize: "14px",
                                }}
                              >
                                <option value="">Select Notice Period</option>
                                <option value="15 Days">15 Days</option>
                                <option value="1 Month">1 Month</option>
                                <option value="2 Months">2 Months</option>
                                <option value="3 Months">3 Months</option>
                              </select>
                            </div>
                            <div className="col-md-6 d-flex align-items-end pb-2">
                              <div className="form-check">
                                <input
                                  className="form-check-input"
                                  type="checkbox"
                                  name="buyout"
                                  id="buyoutCheck2"
                                  checked={candidate_details.buyout}
                                  onChange={handleChange}
                                />
                                <label
                                  className="form-check-label"
                                  htmlFor="buyoutCheck2"
                                  style={{ color: "#353935" }}
                                >
                                  Buyout
                                </label>
                              </div>
                            </div>
                          </>
                        )}

                        {candidate_details.holding_offer === "yes" && (
                          <>
                            <div className="col-md-6">
                              <label
                                className="form-label fw-medium"
                                style={{
                                  color: "#333",
                                  fontSize: "14px",
                                }}
                              >
                                Total Offers *
                              </label>
                              <input
                                type="number"
                                name="total_offers"
                                className="form-control focus:border-makonis-primary focus:ring-2 focus:ring-makonis-primary/25 transition-all duration-300"
                                placeholder="Number of offers"
                                value={candidate_details.total_offers}
                                onChange={handleChange}
                                required
                                style={{
                                  borderRadius: "6px",
                                  border: "1px solid #ddd",
                                  padding: "10px 12px",
                                  fontSize: "14px",
                                }}
                              />
                            </div>
                            <div className="col-md-6">
                              <label
                                className="form-label fw-medium"
                                style={{
                                  color: "#333",
                                  fontSize: "14px",
                                }}
                              >
                                Highest Package in LPA *
                              </label>
                              <input
                                type="number"
                                name="highest_package"
                                className="form-control focus:border-makonis-primary focus:ring-2 focus:ring-makonis-primary/25 transition-all duration-300"
                                placeholder="Highest offer in LPA"
                                value={candidate_details.highest_package}
                                onChange={handleChange}
                                required
                                style={{
                                  borderRadius: "6px",
                                  border: "1px solid #ddd",
                                  padding: "10px 12px",
                                  fontSize: "14px",
                                }}
                              />
                            </div>
                          </>
                        )}
                      </div>
                    </div>

                    <div className="mb-4">
                      <h6
                        className="fw-bold mb-3 d-flex align-items-center gap-2"
                        style={{ color: "#007bff", fontSize: "16px" }}
                      >
                        <FaUser size={16} />
                        Additional Information
                      </h6>

                      <div className="row g-3">
                        <div className="col-md-12">
                          <label
                            className="form-label fw-medium"
                            style={{
                              color: "#333",
                              fontSize: "14px",
                            }}
                          >
                            LinkedIn Profile
                          </label>
                          <input
                            type="url"
                            className="form-control focus:border-makonis-primary focus:ring-2 focus:ring-makonis-primary/25 transition-all duration-300"
                            name="linkedin"
                            placeholder="Your LinkedIn Profile URL"
                            value={candidate_details.linkedin}
                            onChange={handleChange}
                            style={{
                              borderRadius: "6px",
                              border: "1px solid #ddd",
                              padding: "10px 12px",
                              fontSize: "14px",
                            }}
                          />
                        </div>

                        <div className="col-md-12">
                          <label
                            className="form-label fw-medium"
                            style={{
                              color: "#333",
                              fontSize: "14px",
                            }}
                          >
                            Remarks
                          </label>
                          <textarea
                            className="form-control focus:border-makonis-primary focus:ring-2 focus:ring-makonis-primary/25 transition-all duration-300"
                            name="remarks"
                            placeholder="Enter your Remarks"
                            value={candidate_details.remarks}
                            onChange={handleChange}
                            rows="4"
                            style={{
                              borderRadius: "6px",
                              border: "1px solid #ddd",
                              padding: "10px 12px",
                              fontSize: "14px",
                              resize: "vertical",
                            }}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    className="col-lg-4 p-4 h-100 d-flex flex-column"
                    style={{
                      backgroundColor: "#f8f9fa",
                      borderLeft: "1px solid #e9ecef",
                    }}
                  >
                    <div className="flex-grow-1 d-flex flex-column justify-content-center align-items-center">
                      <label
                        htmlFor="resume-upload"
                        className="w-100 text-center p-4 transition-all duration-300 hover:transform hover:-translate-y-0.5"
                        style={{
                          border: "2px dashed #ddd",
                          borderRadius: "12px",
                          backgroundColor: "white",
                          cursor: "pointer",
                          minHeight: "250px",
                          display: "flex",
                          flexDirection: "column",
                          justifyContent: "center",
                          alignItems: "center",
                        }}
                      >
                        <div
                          className="mb-3"
                          style={{
                            width: "60px",
                            height: "60px",
                            backgroundColor: "#00bcd4",
                            borderRadius: "50%",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                          }}
                        >
                          <FaCloudUploadAlt size={24} color="white" />
                        </div>
                        {isLoading ? (
                          <div className="text-center">
                            <FaSpinner
                              className="fa-spin text-primary mb-2"
                              size={34}
                            />
                          </div>
                        ) : selectedFile ? (
                          <div className="mt-2 p-2 bg-light rounded w-100 text-center">
                            <small className="text-success text-break">
                              ✓ {selectedFile.name}
                            </small>
                          </div>
                        ) : (
                          <>
                            <h6
                              className="mb-2"
                              style={{
                                color: "#333",
                                fontSize: "16px",
                                fontWeight: "600",
                              }}
                            >
                              Drag & Drop or Browse
                            </h6>
                            <p
                              className="text-muted mb-3"
                              style={{ fontSize: "14px" }}
                            >
                              to upload your resume
                            </p>
                            <span className="btn btn-outline-primary btn-sm mt-2">
                              Browse Files
                            </span>
                          </>
                        )}
                        <input
                          type="file"
                          id="resume-upload"
                          name="resume"
                          accept=".pdf,.doc,.docx"
                          onChange={handleSubmitResume}
                          required
                          style={{ display: "none" }}
                        />
                      </label>
                    </div>
                    <button
                      type="submit"
                      className="btn btn-lg w-100 mt-4 text-white fw-semibold d-flex align-items-center justify-content-center transition-all duration-300 hover:transform hover:-translate-y-0.5"
                      disabled={waitForSubmission || isLoading}
                      style={{
                        background:
                          "linear-gradient(90deg, #002B59 0%, #009DE6 100%)",
                        border: "none",
                        borderRadius: "8px",
                        padding: "12px 24px",
                        fontSize: "16px",
                        boxShadow: "0 4px 15px rgba(0, 43, 89, 0.3)",
                        gap: "8px",
                      }}
                    >
                      {waitForSubmission ? (
                        <>
                          {" "}
                          <FaSpinner className="fa-spin" size={16} />{" "}
                          <span>Submitting...</span>{" "}
                        </>
                      ) : (
                        <>
                          {" "}
                          <FaPaperPlane size={16} /> <span>Apply Now</span>{" "}
                        </>
                      )}
                    </button>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </Modal>

        <ToastContainer
          position="top-right"
          autoClose={4000}
          hideProgressBar={false}
          newestOnTop
          closeOnClick
          rtl={false}
          pauseOnFocusLoss
          draggable
          pauseOnHover
          theme="colored"
        />
        <style jsx global>{`
          .body-modal-open {
            overflow: hidden !important;
          }
          .fa-spin {
            animation: spin 1s linear infinite !important;
          }
          @keyframes spin {
            0% {
              transform: rotate(0deg);
            }
            100% {
              transform: rotate(360deg);
            }
          }
          .form-control:focus,
          .form-select:focus {
            border-color: #00a0e9 !important;
            box-shadow: 0 0 0 0.2rem rgba(0, 160, 233, 0.25) !important;
          }
        `}</style>
      </div>
    </section>
  );
};

export default Careers;
