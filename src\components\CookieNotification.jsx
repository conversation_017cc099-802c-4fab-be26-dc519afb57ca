import React, { useEffect, useRef } from "react";
import { useCookieConsent } from "../hooks/useCookieConsent";
import { gsap } from "gsap";
import styled, { keyframes } from "styled-components";

// --- Development helpers (same as before) ---
if (typeof window !== "undefined") {
  window.resetCookieConsent = () => {
    localStorage.removeItem("makonis_cookie_consent");
    sessionStorage.removeItem("makonis_banner_shown_session");
    window.location.reload();
  };
  window.showCookieBanner = () => {
    sessionStorage.removeItem("makonis_banner_shown_session");
    window.location.reload();
  };
}

const CookieNotification = () => {
  // NOTE: You'll need to add `declineCookies` to your useCookieConsent hook
  const { showBanner, acceptCookies, declineCookies, isLoading } =
    useCookieConsent();
  const bannerRef = useRef(null);

  useEffect(() => {
    if (showBanner && bannerRef.current && !isLoading) {
      // Animate banner entrance from bottom
      gsap.fromTo(
        bannerRef.current,
        { y: 100, opacity: 0, scale: 0.95 },
        {
          y: 0,
          opacity: 1,
          scale: 1,
          duration: 0.6,
          ease: "back.out(1.7)",
          delay: 0.5,
        }
      );
    }
  }, [showBanner, isLoading]);

  const handleAction = (actionCallback) => {
    if (!bannerRef.current) return;
    // Animate banner exit
    gsap.to(bannerRef.current, {
      opacity: 0,
      y: 100,
      scale: 0.95,
      duration: 0.4,
      ease: "power2.in",
      onComplete: actionCallback,
    });
  };

  if (isLoading || !showBanner) {
    return null;
  }

  return (
    <CookieContainer ref={bannerRef}>
      <CookieCard>
        <ContentWrapper>
          <CookieIconWrapper>
            {/* Make sure you have Font Awesome included in your project for this icon */}
            <i className="fas fa-cookie-bite"></i>
          </CookieIconWrapper>
          <TextWrapper>
            <CookieTitle>We Value Your Privacy</CookieTitle>
            <CookieDescription>
              We use cookies to ensure the best browsing experience and smooth
              functionality of our website. By continuing to use this site, you
              agree to our use of cookies.
            </CookieDescription>
          </TextWrapper>
        </ContentWrapper>

        <CookieActions>
          <ButtonDecline onClick={() => handleAction(declineCookies)}>
            Decline
          </ButtonDecline>
          <ButtonAccept onClick={() => handleAction(acceptCookies)}>
            Accept
          </ButtonAccept>
        </CookieActions>
      </CookieCard>
    </CookieContainer>
  );
};

export default CookieNotification;

// --- STYLES ---
// Styles have been updated to center the banner and set its width.

const cookieBounce = keyframes`
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-4px) rotate(15deg); }
`;

// This container is now a centered wrapper at the bottom of the screen.
const CookieContainer = styled.div`
  position: fixed;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  max-width: 960px; /* Added a max-width for huge screens */
  z-index: 10000;
  opacity: 0; /* Initially hidden for GSAP */

  @media (max-width: 768px) {
    width: 90%;
    bottom: 20px;
  }
`;

// The card now lays out its children in a row on desktop and a column on mobile.
const CookieCard = styled.div`
  width: 100%;
  padding: 20px 24px;
  background: rgba(20, 28, 46, 0.85);
  backdrop-filter: blur(18px);
  -webkit-backdrop-filter: blur(18px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.35);

  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20px;

  /* On screens smaller than 768px, stack the content vertically */
  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;
    padding: 24px;
  }
`;

const ContentWrapper = styled.div`
  display: flex;
  align-items: center;
  gap: 20px;
`;

const CookieIconWrapper = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #0095ff, #0077cc);
  border-radius: 14px;
  box-shadow: 0 8px 20px rgba(0, 149, 255, 0.25);
  flex-shrink: 0;

  i {
    font-size: 1.5rem;
    color: white;
    animation: ${cookieBounce} 2.5s ease-in-out infinite;
  }
`;

const TextWrapper = styled.div`
  display: flex;
  flex-direction: column;
`;

const CookieTitle = styled.h6`
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial, sans-serif;
  font-size: 1.05rem;
  font-weight: 600;
  margin: 0;
  color: #f0f4f8;
  line-height: 1.3;
`;

const CookieDescription = styled.p`
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial, sans-serif;
  font-size: 0.9rem;
  line-height: 1.4;
  color: #a9b8cc;
  margin: 4px 0 0 0;

  @media (max-width: 768px) {
    margin-top: 8px;
  }
`;

const CookieActions = styled.div`
  display: flex;
  gap: 12px;
  flex-shrink: 0;

  @media (max-width: 768px) {
    width: 100%;
    display: grid;
    grid-template-columns: 1fr 1fr;
  }
`;

const ButtonBase = styled.button`
  padding: 10px 20px;
  font-size: 0.95rem;
  font-weight: 600;
  border-radius: 10px;
  border: none;
  cursor: pointer;
  transition: all 0.25s ease;

  &:hover {
    transform: translateY(-2px);
  }
`;

const ButtonDecline = styled(ButtonBase)`
  background-color: rgba(255, 255, 255, 0.1);
  color: #f0f4f8;

  &:hover {
    background-color: rgba(255, 255, 255, 0.15);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  }
`;

const ButtonAccept = styled(ButtonBase)`
  background: linear-gradient(135deg, #0095ff, #0077cc);
  color: white;
  box-shadow: 0 4px 15px rgba(0, 149, 255, 0.2);

  &:hover {
    box-shadow: 0 6px 20px rgba(0, 149, 255, 0.3);
  }
`;
