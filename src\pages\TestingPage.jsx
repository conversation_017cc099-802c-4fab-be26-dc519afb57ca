import React, { useEffect, useState, useRef } from "react";
import { <PERSON> } from "react-router-dom";
import { Container, Row, Col, Card, Button } from "react-bootstrap";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import Test1 from "../assets/Test1.png";
import Test2 from "../assets/Test2.png";
import Test3 from "../assets/Test3.png";
import Test4 from "../assets/Test4.jpg";
import Test5 from "../assets/Test5.jpg";
import Test6 from "../assets/Test6.png";

gsap.registerPlugin(ScrollTrigger);

// The data object is now clean, without style properties in it.
const testingPageData = {
  hero: {
    title: "Excellence in Quality Engineering",
    subtitle:
      "We help businesses thrive by ensuring their software is flawless and high-performing. Our end-to-end testing and quality assurance services cover every stage of development. From functionality to performance, we ensure reliability at every level.",
    backgroundImage: Test1,
  },
  intro: {
    title: "Pioneering Comprehensive Testing Solutions",
    description:
      "At Makonis, we specialize in elevating your software's integrity. By integrating advanced Software Test Engineering, Accessibility Assurance, and Product Engineering, we forge transparent, value-driven partnerships. Our seasoned experts deliver innovative solutions across diverse tech domains, ensuring you achieve your business goals with confidence.",
    image: Test2,
  },
  services: [
    {
      id: "functional",
      title: "Functional Testing",
      icon: "fa-check-circle",
      description:
        "We verify and validate every function of your software to ensure it operates in conformance with your exact specifications. Our meticulous approach guarantees a seamless user experience.",
      features: [
        "Manual & Exploratory Testing",
        "Regression Testing",
        "Integration Testing",
        "User Acceptance Testing (UAT)",
        "Compatibility Testing",
      ],
      image: Test3,
    },
    {
      id: "automation",
      title: "Test Automation",
      icon: "fa-robot",
      description:
        "Accelerate your release cycles and improve accuracy with our Test Automation services. We build robust, scalable automation frameworks tailored to your product's needs.",
      features: [
        "Selenium & Cypress Frameworks",
        "Appium for Mobile Automation",
        "API & Microservices Automation",
        "CI/CD Pipeline Integration",
        "BDD with Cucumber",
      ],
      image: Test4,
    },
    {
      id: "performance",
      title: "Performance Testing",
      icon: "fa-tachometer-alt",
      description:
        "We ensure your application is responsive, reliable, and scalable under any load condition. Pinpoint and eliminate performance bottlenecks before they impact your users.",
      features: [
        "Load & Stress Testing",
        "Endurance & Scalability Testing",
        "Capacity Planning",
        "Bottleneck Analysis",
        "Real-time Performance Monitoring",
      ],
      image: Test6,
    },
    {
      id: "security",
      title: "Security Testing",
      icon: "fa-shield-alt",
      description:
        "Protect your application from modern threats. Our security testing services proactively identify vulnerabilities and ensure your data and users are secure.",
      features: [
        "Vulnerability Assessment",
        "Penetration Testing (Pentesting)",
        "Static & Dynamic Analysis (SAST/DAST)",
        "Compliance Audits (GDPR, HIPAA)",
        "Risk-Based Security Hardening",
      ],
      image: Test5,
    },
  ],
  process: {
    title: "Our Structured Testing Lifecycle",
    description:
      "We follow a proven, systematic approach to ensure maximum test coverage and deliver quantifiable quality improvements for your applications.",
    steps: [
      {
        title: "Discovery & Strategy",
        description:
          "We dive deep into your requirements and architecture to define a tailored, risk-based test strategy.",
        icon: "fa-search",
      },
      {
        title: "Test Planning & Design",
        description:
          "Our team architects comprehensive test plans, crafts detailed test cases, and prepares the optimal test environment.",
        icon: "fa-clipboard-list",
      },
      {
        title: "Execution & Automation",
        description:
          "We execute tests meticulously, automate where it adds value, and rapidly identify and document defects.",
        icon: "fa-cogs",
      },
      {
        title: "Analysis & Reporting",
        description:
          "We track defects to resolution and provide clear, actionable reports with key metrics and quality insights.",
        icon: "fa-chart-pie",
      },
      {
        title: "Optimization & Delivery",
        description:
          "We help you release with confidence, providing feedback for continuous process improvement.",
        icon: "fa-rocket",
      },
    ],
  },
  tools: {
    title: "Our Arsenal of Tools & Technologies",
    description:
      "We utilize a modern, comprehensive tech stack to deliver efficient and effective testing services across all domains.",
    categories: [
      {
        name: "Automation",
        tools: ["Selenium", "Appium", "Cypress", "Playwright", "Katalon Studio", "Robot Framework"],
        icon: "fa-robot",
      },
      {
        name: "Performance",
        tools: ["JMeter", "LoadRunner", "Gatling", "K6", "BlazeMeter", "NeoLoad"],
        icon: "fa-tachometer-alt",
      },
      {
        name: "API Testing",
        tools: ["Postman", "SoapUI", "REST Assured", "Karate DSL", "ReadyAPI", "Pact"],
        icon: "fa-code-branch",
      },
      {
        name: "DevOps & CI/CD",
        tools: ["Jenkins", "Docker", "Kubernetes", "Azure DevOps", "GitLab CI", "CircleCI"],
        icon: "fa-cogs",
      },
    ],
  },
  stats: [
    { value: "500+", label: "Successful Projects", icon: "fa-project-diagram" },
    { value: "99%", label: "Client Satisfaction", icon: "fa-smile-beam" },
    { value: "40%", label: "Avg. Time-to-Market Reduction", icon: "fa-shipping-fast" },
    { value: "24/7", label: "Dedicated Support", icon: "fa-headset" },
  ],
  cta: {
    title: "Ready to Elevate Your Software Quality?",
    text: "Partner with us to build robust, reliable, and remarkable applications. Let's discuss how our testing expertise can drive your project's success.",
    buttonText: "Schedule a Consultation",
    buttonLink: "/contact",
  },
};

const TestingPage = () => {
  const [activeService, setActiveService] = useState("functional");
  const heroRef = useRef(null);
  const sectionsRef = useRef([]);
  const processTimelineRef = useRef(null);

  const secondaryColor = "#00a0e9";

  const gradientTextStyle = {
    fontSize: "clamp(2.5rem, 6vw, 3.5rem)",
    lineHeight: "1.2",
    fontWeight: "bold",
    background: "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
    WebkitBackgroundClip: "text",
    WebkitTextFillColor: "transparent",
    backgroundClip: "text",
    textShadow: "0 0 40px rgba(0, 160, 233, 0.4)",
  };

  const glassCardStyle = {
    background: "rgba(255, 255, 255, 0.05)",
    backdropFilter: "blur(15px)",
    border: "1px solid rgba(255, 255, 255, 0.1)",
    borderRadius: "1rem",
    transition: "all 0.4s ease",
  };

  useEffect(() => {
    // Scroll to top is now handled globally by useScrollToTop hook

    const ctx = gsap.context(() => {
      if (heroRef.current) {
        gsap.from(heroRef.current.querySelector("h1"), { y: 100, opacity: 0, duration: 1.2, ease: "power3.out", delay: 0.2 });
        gsap.from(heroRef.current.querySelector("p"), { y: 50, opacity: 0, duration: 1, ease: "power2.out", delay: 0.5 });
        gsap.from(heroRef.current.querySelector(".card-makonis-glass"), { y: 50, opacity: 0, duration: 1.2, ease: "power3.out", delay: 0.8 });
      }

      sectionsRef.current.forEach((section) => {
        const targets = section.querySelectorAll(".animate-in");
        if (targets.length > 0) {
          gsap.from(targets, {
            y: 60,
            opacity: 0,
            duration: 1,
            ease: "power3.out",
            stagger: 0.2,
            scrollTrigger: {
              trigger: section,
              start: "top 85%",
              toggleActions: "play none none reverse",
            },
          });
        }
      });

      if (processTimelineRef.current) {
        const timeline = processTimelineRef.current;
        const items = gsap.utils.toArray(".process-item-enhanced");

        gsap.from(timeline.querySelector(".process-track-line"), {
          scaleY: 0,
          transformOrigin: "top",
          ease: "none",
          scrollTrigger: {
            trigger: timeline,
            start: "top center",
            end: "bottom bottom-=150",
            scrub: true,
          },
        });

        items.forEach((item) => {
          const card = item.querySelector(".process-card-enhanced");
          const dot = item.querySelector(".process-dot-enhanced");

          gsap.from([dot, card], {
            autoAlpha: 0,
            y: 50,
            stagger: 0.1,
            ease: "power2.out",
            scrollTrigger: {
              trigger: item,
              start: "top center+=100",
              toggleActions: "play none none reverse",
            }
          });
        });
      }
    });

    return () => {
      ScrollTrigger.getAll().forEach(t => t.kill());
      ctx.revert();
    };
  }, []);

  const addToRefs = (el) => {
    if (el && !sectionsRef.current.includes(el)) {
      sectionsRef.current.push(el);
    }
  };

  return (
    <div
      className="testing-page relative overflow-hidden"
      style={{ background: "linear-gradient(135deg, rgba(0, 41, 86, 0.95) 0%, rgba(0, 41, 86, 0.98) 100%)", backdropFilter: "blur(10px)", color: "rgba(255, 255, 255, 0.9)" }}
    >
      {/* Background Grid Pattern */}
      <div className="absolute inset-0 z-0">
        <div
          className="absolute inset-0 opacity-20"
          style={{
            backgroundImage: `linear-gradient(rgba(0, 160, 233, 0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(0, 160, 233, 0.1) 1px, transparent 1px)`,
            backgroundSize: "50px 50px",
          }}
        />
      </div>
      <section
        ref={heroRef}
        className="testing-hero d-flex align-items-center text-white position-relative overflow-hidden"
        style={{
          background: "linear-gradient(135deg, rgba(0, 41, 86, 0.95) 0%, rgba(0, 41, 86, 0.98) 100%)",
          backdropFilter: "blur(10px)",
          minHeight: "100vh",
          position: "relative",
        }}
      >
        <div
          className="position-absolute w-100 h-100"
          style={{
            backgroundImage: `linear-gradient(rgba(0, 160, 233, 0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(0, 160, 233, 0.1) 1px, transparent 1px)`,
            backgroundSize: "50px 50px",
            opacity: 0.2,
            pointerEvents: "none",
          }}
        />
        <div className="position-absolute w-100 h-100" style={{ zIndex: 1 }}>
          {[...Array(6)].map((_, i) => (
            <div
              key={i}
              className="position-absolute rounded-circle"
              style={{
                width: `${Math.random() * 100 + 50}px`,
                height: `${Math.random() * 100 + 50}px`,
                background: `rgba(0, 160, 233, ${Math.random() * 0.1 + 0.05})`,
                top: `${Math.random() * 100}%`,
                left: `${Math.random() * 100}%`,
                animation: `float ${Math.random() * 10 + 10}s infinite ease-in-out`,
                backdropFilter: "blur(1px)",
              }}
            />
          ))}
        </div>
        <Container className="position-relative" style={{ zIndex: 2 }}>
          <Row className="align-items-center">
            <Col lg={7} className="text-white text-center text-lg-start">
              <h1 className="display-1 fw-bold mb-3 pb-2" style={gradientTextStyle}>
                {testingPageData.hero.title}
              </h1>
              <p
                className="mb-5"
                style={{
                  fontSize: "1.25rem",
                  lineHeight: "1.6",
                  color: "rgba(255,255,255,0.8)",
                }}
              >
                {testingPageData.hero.subtitle}
              </p>
            </Col>
            <Col lg={5}>
              <div className="position-relative">
                <div
                  className="card-makonis-glass p-4"
                  style={{
                    background: "rgba(255, 255, 255, 0.08)",
                    backdropFilter: "blur(20px)",
                    border: "1px solid rgba(255, 255, 255, 0.2)",
                    borderRadius: "1rem",
                  }}
                >
                  <img
                    src={testingPageData.hero.backgroundImage}
                    alt="Quality Assurance & Testing"
                    className="w-100 rounded-3"
                    style={{ height: "300px", objectFit: "cover" }}
                  />
                </div>
              </div>
            </Col>
          </Row>
        </Container>
      </section>

      <section ref={addToRefs} className="py-5 my-5">
        <Container>
          <Row className="align-items-center g-5">
            <Col lg={7} className="animate-in">
              <h2 style={gradientTextStyle} className="pb-2">{testingPageData.intro.title}</h2>
              <p className="lead mt-4 mb-4">
                {testingPageData.intro.description}
              </p>
            </Col>
            <Col lg={5} className="animate-in">
              <div className="p-3" style={glassCardStyle}>
                <img
                  src={testingPageData.intro.image}
                  alt="Our Team"
                  className="img-fluid rounded-3"
                  style={{ minHeight: "350px", objectFit: "cover" }}
                />
              </div>
            </Col>
          </Row>
        </Container>
      </section>

      <section ref={addToRefs} className="py-5">
        <Container>
          <Row className="g-4">
            {testingPageData.stats.map((stat, index) => (
              <Col md={6} lg={3} key={index} className="animate-in">
                <div
                  className="text-center p-4 h-100 stat-card"
                  style={glassCardStyle}
                >
                  <i
                    className={`fas ${stat.icon} fa-3x mb-3`}
                    style={{ color: secondaryColor }}
                  ></i>
                  <h3 className="display-5 fw-bold text-white">{stat.value}</h3>
                  <p className="mb-0 text-white-50">{stat.label}</p>
                </div>
              </Col>
            ))}
          </Row>
        </Container>
      </section>

      <section ref={addToRefs} className="py-5 my-5">
        <Container>
          <div className="text-center mb-5 animate-in">
            <h2 style={gradientTextStyle} className="pb-2">
              A Full Spectrum of Testing Services
            </h2>
            <p className="lead mx-auto" style={{ maxWidth: "700px" }}>
              We offer a wide range of testing services to ensure your
              applications meet the highest quality standards.
            </p>
          </div>
          <div className="d-flex justify-content-center flex-wrap gap-2 mb-5 animate-in">
            {testingPageData.services.map((service) => (
              <Button
                key={service.id}
                variant={
                  activeService === service.id ? "primary" : "outline-secondary"
                }
                className="rounded-pill px-4 py-2"
                style={
                  activeService === service.id
                    ? {
                        background: secondaryColor,
                        borderColor: secondaryColor,
                      }
                    : {}
                }
                onClick={() => setActiveService(service.id)}
              >
                <i className={`fas ${service.icon} me-2`}></i>
                {service.title}
              </Button>
            ))}
          </div>
          <div className="mt-4">
            {testingPageData.services.map((service) => (
              <div
                key={service.id}
                className={`${
                  activeService === service.id ? "d-block" : "d-none"
                }`}
              >
                <div className="p-4" style={glassCardStyle}>
                  <Row className="g-5 align-items-center">
                    <Col lg={6}>
                      <img
                        src={service.image}
                        alt={service.title}
                        className="img-fluid rounded-3 w-100"
                        style={{ height: "400px", objectFit: "cover" }}
                      />
                    </Col>
                    <Col lg={6}>
                      {/* THIS IS THE CORRECTED PART */}
                      <h3 className="display-6 mb-3 pb-1" style={gradientTextStyle}>
                        {service.title}
                      </h3>
                      <p>{service.description}</p>
                      <ul className="list-unstyled mt-4">
                        {service.features.map((feature, index) => (
                          <li
                            key={index}
                            className="d-flex align-items-center mb-2"
                          >
                            <i
                              className="fas fa-check-circle me-2"
                              style={{ color: secondaryColor }}
                            ></i>
                            {feature}
                          </li>
                        ))}
                      </ul>
                    </Col>
                  </Row>
                </div>
              </div>
            ))}
          </div>
        </Container>
      </section>

      <section className="py-5 my-5">
        <Container>
          <div className="text-center mb-5 pb-5 animate-in">
            <h2 style={gradientTextStyle} className="pb-1">
              {testingPageData.process.title}
            </h2>
            <p
              className="lead mx-auto"
              style={{ maxWidth: "700px" }}
            >
              {testingPageData.process.description}
            </p>
          </div>
          <div ref={processTimelineRef} className="process-timeline-enhanced">
            <div className="process-track-line"></div>
            {testingPageData.process.steps.map((step, index) => (
              <div key={index} className={`process-item-enhanced ${index % 2 === 0 ? 'left-side' : 'right-side'}`}>
                <div className="process-card-enhanced">
                  <div className="process-card-content">
                    <span className="process-card-number">{step.number}</span>
                    <i className={`fas ${step.icon} process-card-icon`}></i>
                    <h3
                      className="h5 fw-bold mt-3 mb-2"
                      style={{ color: secondaryColor }}
                    >
                      {step.title}
                    </h3>
                    <p className="mb-0 small text-white-50">
                      {step.description}
                    </p>
                  </div>
                </div>
                <div className="process-dot-enhanced"></div>
              </div>
            ))}
          </div>
        </Container>
      </section>

      <section ref={addToRefs} className="py-5 my-5">
        <Container>
          <div className="text-center mb-5 animate-in">
            <h2 style={gradientTextStyle} className="pb-1">{testingPageData.tools.title}</h2>
            <p className="lead mx-auto" style={{ maxWidth: "700px" }}>
              {testingPageData.tools.description}
            </p>
          </div>
          <Row className="g-4">
            {testingPageData.tools.categories.map((category, categoryIndex) => (
              <Col lg={3} md={4} sm={6} key={category.name} className="animate-in">
                <div className="tools-category-card h-100">
                  <div className="category-header">
                    <div className="category-icon-wrapper">
                      <i className={`fas ${category.icon}`}></i>
                    </div>
                    <h3 className="category-title">{category.name}</h3>
                  </div>
                  <div className="tools-grid">
                    {category.tools.map((tool, toolIndex) => (
                      <div key={toolIndex} className="tool-item">
                        <span className="tool-name">{tool}</span>
                        <div className="tool-shine"></div>
                      </div>
                    ))}
                  </div>
                  <div className="floating-particles">
                    {[...Array(3)].map((_, i) => (
                      <div
                        key={i}
                        className="particle"
                        style={{
                          animationDelay: `${i * 0.5 + categoryIndex * 0.2}s`,
                        }}
                      />
                    ))}
                  </div>
                </div>
              </Col>
            ))}
          </Row>
        </Container>
      </section>
  
      <style>{`
        @keyframes float {
          0%, 100% { transform: translateY(0) rotate(0deg); }
          50% { transform: translateY(-20px) rotate(10deg); }
        }

        .stat-card:hover {
          transform: translateY(-8px) scale(1.02);
          box-shadow: 0 20px 40px rgba(0, 160, 233, 0.2);
          border-color: rgba(0, 160, 233, 0.5);
        }

        /* --- ADDED CSS FOR BUTTONS --- */
        .testing-page .btn-outline-secondary {
          transition: all 0.3s ease;
          border-color: rgba(255, 255, 255, 0.4);
          color: rgba(255, 255, 255, 0.8);
        }

        .testing-page .btn-outline-secondary:hover {
          color: ${secondaryColor};
          border-color: ${secondaryColor};
          background-color: transparent; /* This ensures the background remains dark */
        }
        /* --- END OF ADDED CSS --- */

        .process-timeline-enhanced {
          position: relative;
          width: 100%;
          max-width: 800px;
          margin: 0 auto;
          padding: 2rem 0;
          display: flex;
          flex-direction: column;
          align-items: center;
        }
        
        .process-track-line {
          position: absolute;
          top: 0;
          left: 50%;
          height: 100%;
          transform: translateX(-50%);
          width: 4px;
          background: linear-gradient(to bottom, ${secondaryColor}, transparent);
          box-shadow: 0 0 10px ${secondaryColor}, 0 0 20px ${secondaryColor};
          border-radius: 2px;
          z-index: 1;
        }

        .process-item-enhanced {
          position: relative;
          width: 100%;
          display: flex;
          margin-bottom: 3rem; 
          z-index: 2;
        }

        .process-item-enhanced.right-side {
          justify-content: flex-end;
        }

        .process-item-enhanced.left-side {
          justify-content: flex-start;
        }
        
        .process-card-enhanced {
          position: relative;
          padding: 2rem;
          width: calc(50% - 40px);
          background-color: rgba(255, 255, 255, 0.07);
          border: 1px solid rgba(255, 255, 255, 0.15);
          border-radius: 1.5rem;
          backdrop-filter: blur(12px);
          transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }

        .process-item-enhanced.left-side .process-card-enhanced {
          text-align: right;
        }
        
        .process-card-enhanced::before {
          content: "";
          position: absolute;
          top: 0; left: 0; right: 0; bottom: 0;
          background: radial-gradient(circle at center, rgba(0, 160, 233, 0.15) 0%, transparent 50%);
          border-radius: inherit;
          opacity: 0;
          transition: opacity 0.5s ease;
          pointer-events: none;
        }
        
        .process-item-enhanced:hover .process-card-enhanced::before {
            opacity: 1;
        }

        .process-card-content {
          position: relative;
          z-index: 2;
        }

        .process-card-number {
          position: absolute;
          top: 0.5rem;
          font-size: 2.5rem;
          font-weight: 800;
          color: rgba(255, 255, 255, 0.05);
          z-index: -1;
        }
        .process-item-enhanced.right-side .process-card-number { right: 1rem; }
        .process-item-enhanced.left-side .process-card-number { left: 1rem; }

        .process-card-icon {
          font-size: 1.75rem;
          color: rgba(255, 255, 255, 0.5);
          margin-bottom: 1rem;
          display: block;
        }
        
        .process-dot-enhanced {
          position: absolute;
          top: 1rem;
          left: 50%;
          transform: translateX(-50%);
          width: 20px;
          height: 20px;
          background: #0f172a;
          border: 4px solid ${secondaryColor};
          border-radius: 50%;
          z-index: 10;
          box-shadow: 0 0 15px ${secondaryColor}, 0 0 25px ${secondaryColor};
        }
        
        .tools-category-card {
          background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.02) 100%);
          backdrop-filter: blur(20px);
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 1.5rem;
          padding: 2rem 1.5rem;
          position: relative;
          overflow: hidden;
          transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
          cursor: pointer;
        }

        .tools-category-card:hover {
          transform: translateY(-8px) scale(1.02);
          border-color: rgba(0, 160, 233, 0.4);
          box-shadow: 0 25px 50px rgba(0, 160, 233, 0.15), 0 0 0 1px rgba(0, 160, 233, 0.2);
        }

        .tools-category-card::before {
          content: '';
          position: absolute;
          top: 0; left: 0; right: 0; bottom: 0;
          background: linear-gradient(135deg, ${secondaryColor}10 0%, transparent 50%, ${secondaryColor}05 100%);
          opacity: 0;
          transition: opacity 0.4s ease;
          z-index: 0;
        }

        .tools-category-card:hover::before { opacity: 1; }
        .category-header { text-align: center; margin-bottom: 1.5rem; position: relative; z-index: 2; }
        .category-icon-wrapper {
          width: 60px; height: 60px; margin: 0 auto 1rem;
          background: linear-gradient(135deg, ${secondaryColor}20, ${secondaryColor}40);
          border-radius: 50%; display: flex; align-items: center; justify-content: center;
          border: 2px solid rgba(0, 160, 233, 0.3); transition: all 0.3s ease;
        }

        .tools-category-card:hover .category-icon-wrapper {
          transform: scale(1.1) rotate(5deg);
          box-shadow: 0 10px 25px rgba(0, 160, 233, 0.3);
        }

        .category-icon-wrapper i { font-size: 1.5rem; color: ${secondaryColor}; }
        .category-title { font-size: 1.25rem; font-weight: 600; color: white; margin: 0; text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3); }
        .tools-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 0.75rem; position: relative; z-index: 2; }
        .tool-item {
          background: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 0.75rem; padding: 0.75rem 1rem; text-align: center;
          position: relative; overflow: hidden; transition: all 0.3s ease;
        }

        .tool-item:hover {
          background: rgba(0, 160, 233, 0.1); border-color: rgba(0, 160, 233, 0.3);
          transform: translateY(-2px);
        }

        .tool-name { font-size: 0.875rem; font-weight: 500; color: rgba(255, 255, 255, 0.9); position: relative; z-index: 2; }
        .tool-shine {
          position: absolute; top: 0; left: -100%; width: 100%; height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
          transition: left 0.5s ease;
        }

        .tool-item:hover .tool-shine { left: 100%; }
        .floating-particles { position: absolute; top: 0; left: 0; right: 0; bottom: 0; pointer-events: none; z-index: 1; }
        .particle {
          position: absolute; width: 4px; height: 4px; background: ${secondaryColor};
          border-radius: 50%; opacity: 0.6; animation: particleFloat 8s ease-in-out infinite;
        }

        .particle:nth-child(1) { top: 20%; left: 15%; }
        .particle:nth-child(2) { top: 60%; right: 20%; }
        .particle:nth-child(3) { bottom: 25%; left: 70%; }

        @keyframes particleFloat {
          0%, 100% { transform: translateY(0px) translateX(0px) scale(1); opacity: 0.6; }
          25% { transform: translateY(-10px) translateX(5px) scale(1.2); opacity: 0.8; }
          50% { transform: translateY(-5px) translateX(-3px) scale(0.8); opacity: 0.4; }
          75% { transform: translateY(-15px) translateX(8px) scale(1.1); opacity: 0.7; }
        }
        
        @media (max-width: 991px) {
            .testing-hero { min-height: auto; padding: 5rem 0; }
        }
        
        @media (max-width: 767px) {
            .process-track-line {
              left: 20px;
              transform: translateX(0);
            }
            .process-item-enhanced {
              width: auto;
              margin-left: 50px;
              justify-content: flex-start;
            }
            .process-item-enhanced.right-side,
            .process-item-enhanced.left-side {
              justify-content: flex-start;
              width: calc(100% - 50px);
            }
            .process-card-enhanced {
              width: 100%;
              text-align: left !important;
            }
            .process-dot-enhanced {
              left: 20px;
            }
        }
      `}</style>
    </div>
  );
};

export default TestingPage;