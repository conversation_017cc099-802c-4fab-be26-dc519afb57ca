import { useEffect, useState, useRef } from "react";
import { Link } from "react-router-dom";
import { Container, Row, Col, Image, ListGroup } from "react-bootstrap";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import "animate.css";
import Contact1 from "../assets/Contact1.png";
import Contact2 from "../assets/Contact2.png";
import Contact3 from "../assets/Contact3.png";

gsap.registerPlugin(ScrollTrigger);

const contractToHireData = {
  hero: {
    title: "Contract to Hire Staffing Services",
    subtitle:
      "Flexible Hiring Solutions with Permanent Potential. Our contract-to-hire model allows companies and candidates to evaluate each other before committing to full-time employment, ensuring the perfect match for long-term success.",
    backgroundImage:
      "https://images.unsplash.com/photo-1600880292203-757bb62b4baf?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1926&q=80",
  },
  sections: [
    {
      id: "benefits",
      title: "Benefits of Contract to Hire Staffing",
      texts: [""],
      listItems: [
        "Risk Mitigation: Evaluate candidates' performance, cultural fit, and skills before making permanent hiring decisions.",
        "Cost-Effective: Reduce recruitment costs and minimize the risk of expensive hiring mistakes through extended evaluation periods.",
        "Flexibility: Adapt to changing business needs while maintaining access to skilled professionals for project-based work.",
        "Faster Onboarding: Quick access to pre-screened, qualified candidates who can start immediately on contract basis.",
        "Talent Pipeline: Build relationships with high-quality contractors who may become permanent employees.",
        "Reduced Administrative Burden: We handle payroll, benefits, and compliance during the contract period.",
      ],
      image: Contact1,
      alt: "Benefits of contract to hire staffing services",
      reversed: false,
    },
    {
      id: "process",
      title: "Our Contract to Hire Process",
      texts: [""],
      listItems: [
        "Needs Assessment: We work closely with you to understand your specific role requirements, company culture, and long-term hiring goals.",
        "Candidate Sourcing: Access to our extensive network of qualified professionals across various industries and skill sets.",
        "Screening & Evaluation: Comprehensive vetting process including skills assessment, background checks, and cultural fit evaluation.",
        "Contract Placement: Seamless onboarding of selected candidates on a contract basis with clear conversion terms.",
        "Performance Monitoring: Regular check-ins and performance evaluations during the contract period to ensure success.",
        "Conversion Support: Smooth transition to permanent employment when both parties are ready to commit.",
      ],
      image: Contact2,
      alt: "Contract to hire staffing process",
      reversed: true,
    },
    {
      id: "why-work-with-us",
      title: "Why Choose Our Contract to Hire Services?",
      texts: [""],
      listItems: [
        "Proven Track Record: High success rate in converting contract positions to permanent placements with satisfied clients and candidates.",
        "Industry Expertise: Deep understanding of various industries including IT, healthcare, finance, engineering, and professional services.",
        "Quality Candidates: Access to a large database of pre-vetted professionals with specialized skills and experience.",
        "Flexible Terms: Customizable contract periods and conversion terms to meet your specific business needs.",
        "Comprehensive Support: Full-service approach including payroll management, benefits administration, and compliance handling.",
        "Dedicated Account Management: Single point of contact for seamless communication and relationship management.",
      ],
      image: Contact3,
      alt: "Why choose our contract to hire services",
      reversed: false,
    },
  ],
};

const ContractToHireStaffingPage = () => {
  const heroRef = useRef(null);
  const sectionsRef = useRef([]);

  useEffect(() => {
    window.scrollTo(0, 0);

    // Hero animation
    if (heroRef.current) {
      gsap.fromTo(
        heroRef.current.querySelectorAll(".hero-content > *"),
        { opacity: 0, y: 50 },
        {
          opacity: 1,
          y: 0,
          duration: 1,
          stagger: 0.2,
          ease: "power3.out",
        }
      );
    }

    // Section animations
    sectionsRef.current.forEach((section, index) => {
      if (section) {
        gsap.fromTo(
          section.querySelectorAll(".animate-in"),
          { opacity: 0, y: 30 },
          {
            opacity: 1,
            y: 0,
            duration: 0.8,
            stagger: 0.1,
            ease: "power2.out",
            scrollTrigger: {
              trigger: section,
              start: "top 80%",
              end: "bottom 20%",
              toggleActions: "play none none reverse",
            },
          }
        );
      }
    });

    return () => {
      ScrollTrigger.getAll().forEach((trigger) => trigger.kill());
    };
  }, []);

  const addToRefs = (el) => {
    if (el && !sectionsRef.current.includes(el)) {
      sectionsRef.current.push(el);
    }
  };

  return (
    <div className="contract-to-hire-page">
      {/* Hero Section */}
      <section
        ref={heroRef}
        className="hero-section position-relative d-flex align-items-center justify-content-center text-center text-white overflow-hidden"
        style={{
          minHeight: "100vh",
          background: `linear-gradient(135deg, rgba(0, 41, 86, 0.9)100%), url(${contractToHireData.hero.backgroundImage})`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundAttachment: "fixed",
        }}
      >
        {/* Animated Background Elements */}
      

        <Container className="position-relative z-index-2">
          <div className="hero-content">
            <h1
              className="display-3 fw-bold mb-4"
              style={{
                fontSize: "clamp(1.875rem, 5vw, 3rem)",
                lineHeight: "1.2",
                background:
                  "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
                WebkitBackgroundClip: "text",
                WebkitTextFillColor: "transparent",
                backgroundClip: "text",
                textShadow: "0 0 40px rgba(0, 160, 233, 0.4)",
              }}
            >
              {contractToHireData.hero.title}
            </h1>
            <p
              className="lead mb-5 mx-auto"
              style={{
              maxWidth: "1100px",
              textShadow: "1px 1px 3px rgba(0,0,0,0.4)",
              fontSize: "clamp(1rem, 2.5vw, 1.3rem)",
                color: "rgba(255, 255, 255, 0.9)",
              }}
            >
              {contractToHireData.hero.subtitle}
            </p>
            {/* <div className="d-flex flex-column flex-sm-row gap-3 justify-content-center">
              <Link
                to="/contact"
                className="btn btn-lg px-5 py-3 rounded-pill fw-semibold text-decoration-none"
                style={{
                  background: "linear-gradient(135deg, #00a0e9 0%, #002956 100%)",
                  border: "none",
                  color: "white",
                  boxShadow: "0 8px 25px rgba(0, 160, 233, 0.3)",
                  transition: "all 0.3s ease",
                }}
                onMouseEnter={(e) => {
                  e.target.style.transform = "translateY(-2px)";
                  e.target.style.boxShadow = "0 12px 35px rgba(0, 160, 233, 0.4)";
                }}
                onMouseLeave={(e) => {
                  e.target.style.transform = "translateY(0)";
                  e.target.style.boxShadow = "0 8px 25px rgba(0, 160, 233, 0.3)";
                }}
              >
                Get Started Today
              </Link>
              <Link
                to="/services"
                className="btn btn-outline-light btn-lg px-5 py-3 rounded-pill fw-semibold text-decoration-none"
                style={{
                  borderWidth: "2px",
                  transition: "all 0.3s ease",
                }}
                onMouseEnter={(e) => {
                  e.target.style.background = "rgba(255, 255, 255, 0.1)";
                  e.target.style.transform = "translateY(-2px)";
                }}
                onMouseLeave={(e) => {
                  e.target.style.background = "transparent";
                  e.target.style.transform = "translateY(0)";
                }}
              >
                Learn More
              </Link>
            </div> */}
          </div>
        </Container>
      </section>

      {/* Main Content Sections */}
      <div
        className="main-content pb-5"
        style={{
          background: "linear-gradient(135deg, #002956 0%, #001a35 50%, #002956 100%)",
          minHeight: "100vh",
        }}
      >
        <Container>
          {contractToHireData.sections.map((section, idx) => (
            <section
              key={section.id}
              ref={addToRefs}
              className=" mb-md-6 py-3"
            >
              {/* Centered Heading Above Content */}
              <Row>
                <Col xs={12}>
                  <h2
                    className="text-center"
                    style={{
                      fontSize: "clamp(1.5rem, 4vw, 2.25rem)",
                      fontWeight: "800",
                      letterSpacing: "2.6px",
                      paddingTop: "2rem" ,
                      paddingBottom: "2rem",
                      background:
                        "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
                      WebkitBackgroundClip: "text",
                      WebkitTextFillColor: "transparent",
                      backgroundClip: "text",
                      textShadow: "0 0 30px rgba(0, 160, 233, 0.3)",
                    }}
                  >
                    {section.title}
                  </h2>
                  {/* Enhanced Accent Line */}
                  <div className="w-30 h-1 mx-auto relative mb-5">
                    <div
                      className="w-full h-full rounded-sm shadow-glow"
                      style={{
                        background:
                          "linear-gradient(90deg, transparent, #00a0e9, transparent)",
                      }}
                    />
                  </div>
                </Col>
              </Row>

              {/* Content and Image Row */}
              <Row
                className={`align-items-center g-4 g-lg-5 ${
                  section.reversed ? "flex-row-reverse" : ""
                }`}
              >
                <Col
                  lg={6}
                  md={12}
                  className={`${section.reversed ? "ps-lg-4" : "pe-lg-4"}`}
                >
                  <div className="content-wrapper">
                    {section.texts.map((text, textIdx) => (
                      <p
                        key={textIdx}
                        className="mb-3 mb-md-4 animate-in"
                        style={{
                          fontSize: "1.2rem",
                          lineHeight: "1.7",
                          color: "rgba(255, 255, 255, 0.9)",
                        }}
                      >
                        {text}
                      </p>
                    ))}

                    {section.listItems && (
                      <ListGroup
                        variant="flush"
                        className="bg-transparent animate-in"
                      >
                        {section.listItems.map((item, itemIdx) => (
                          <ListGroup.Item
                            key={itemIdx}
                            className="bg-transparent border-0 px-0 py-2"
                            style={{
                              color: "rgba(255, 255, 255, 0.85)",
                              fontSize: "1.1rem",
                              lineHeight: "1.6",
                            }}
                          >
                            <div className="d-flex align-items-start">
                              <span
                                className="me-3 mt-1 flex-shrink-0"
                                style={{
                                  width: "8px",
                                  height: "8px",
                                  background: "#00a0e9",
                                  borderRadius: "50%",
                                  display: "inline-block",
                                }}
                              />
                              <span>{item}</span>
                            </div>
                          </ListGroup.Item>
                        ))}
                      </ListGroup>
                    )}
                  </div>
                </Col>

                <Col lg={6} md={12} className="text-center">
                  <div
                    className="image-wrapper animate-in"
                    style={{
                      borderRadius: "20px",
                      overflow: "hidden",
                      boxShadow: "0 20px 60px rgba(0, 160, 233, 0.2)",
                      background:
                        "linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(0,160,233,0.1) 100%)",
                      padding: "2px",
                    }}
                  >
                    <Image
                      src={section.image}
                      alt={section.alt}
                      fluid
                      style={{
                        borderRadius: "18px",
                        width: "100%",
                        height: "auto",
                        objectFit: "cover",
                      }}
                    />
                  </div>
                </Col>
              </Row>
            </section>
          ))}
        </Container>
      </div>

  

      {/* Floating Animation Styles */}
      <style>{`
        @keyframes float {
          0%, 100% {
            transform: translateY(0px);
          }
          50% {
            transform: translateY(-20px);
          }
        }

        .shadow-glow {
          box-shadow: 0 0 20px rgba(0, 160, 233, 0.5);
        }

        .z-index-2 {
          z-index: 2;
        }
      `}</style>
    </div>
  );
};

export default ContractToHireStaffingPage;
