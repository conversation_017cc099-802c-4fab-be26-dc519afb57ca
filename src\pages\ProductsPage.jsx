import React, { useRef, useEffect, useState } from 'react';
import { Container, Row, Col } from 'react-bootstrap';
import { FaRocket, FaChartLine, FaShieldAlt, FaArrowUp } from 'react-icons/fa';
import { gsap } from 'gsap';
import ATSDemo from './ATSDemo';
import MakoPlus from './MakoPlus';
import TradingIntelligence from './TradingIntelligence';

const ProductsPage = () => {
  // Refs for scroll targets and animations
  const heroRef = useRef(null);
  const titleRef = useRef(null);
  const subtitleRef = useRef(null);
  const buttonsRef = useRef(null);
  const atsDemoRef = useRef(null);
  const makoPlusRef = useRef(null);
  const tradingIntelligenceRef = useRef(null);

  // State for scroll-to-top button
  const [showScrollTop, setShowScrollTop] = useState(false);

  // <PERSON>le scroll to show/hide back to top button
  useEffect(() => {
    const handleScroll = () => {
      setShowScrollTop(window.scrollY > 500);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Hero section animations
  useEffect(() => {
    const ctx = gsap.context(() => {
      const tl = gsap.timeline({ delay: 0.2 });

      // Title animation
      if (titleRef.current) {
        tl.from(titleRef.current, {
          y: -50,
          opacity: 0,
          duration: 1,
          ease: "power3.out",
        });
      }

      // Subtitle animation
      if (subtitleRef.current) {
        tl.from(subtitleRef.current, {
          y: 30,
          opacity: 0,
          duration: 0.8,
          ease: "power2.out",
        }, "-=0.5");
      }

      // Buttons animation
      if (buttonsRef.current) {
        // Ensure buttons are visible first
        gsap.set(buttonsRef.current.children, { opacity: 1, y: 0 });

        tl.from(buttonsRef.current.children, {
          y: 50,
          opacity: 0,
          duration: 0.8,
          ease: "power2.out",
          stagger: 0.15,
          onComplete: () => {
            // Ensure buttons are visible after animation
            gsap.set(buttonsRef.current.children, { opacity: 1, y: 0 });
          }
        }, "-=0.4");
      }
    }, heroRef);

    return () => ctx.revert();
  }, []);

  // Smooth scroll function
  const scrollToSection = (ref) => {
    if (ref.current) {
      ref.current.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
      });
    }
  };

  // Scroll to top function
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  };

  return (
    <>
      <style>{`
        /* Hero Section Styles */
        .products-hero {
          min-height: 100vh;
          background: linear-gradient(135deg, rgba(0, 41, 86, 0.95) 0%, rgba(0, 41, 86, 0.98) 100%);
          display: flex;
          align-items: center;
          position: relative;
          overflow: hidden;
        }

        .products-hero::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-image: 
            linear-gradient(rgba(0, 160, 233, 0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(0, 160, 233, 0.1) 1px, transparent 1px);
          background-size: 50px 50px;
          opacity: 0.3;
          z-index: 1;
        }

        .products-hero-content {
          position: relative;
          z-index: 2;
          text-align: center;
          color: white;
        }

        .products-hero-title {
          font-size: clamp(2.5rem, 8vw, 4rem);
          font-weight: 800;
          line-height: 1.2;
          margin-bottom: 2rem;
          background: linear-gradient(135deg, #ffffff 0%, #00a0e9 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
          text-shadow: 0 0 30px rgba(0, 160, 233, 0.3);
        }

        .products-hero-subtitle {
          font-size: 1.25rem;
          line-height: 1.6;
          margin-bottom: 3rem;
          max-width: 800px;
          margin-left: auto;
          margin-right: auto;
          opacity: 0.9;
        }

        /* Navigation Buttons */
        .products-nav-buttons {
          display: flex;
          flex-wrap: wrap;
          justify-content: center;
          gap: 1.5rem;
          margin-top: 2rem;
          position: relative;
          z-index: 10;
          min-height: 60px;
        }

        .product-nav-btn {
          background: rgba(255, 255, 255, 0.15);
          border: 2px solid rgba(255, 255, 255, 0.3);
          color: white;
          padding: 1rem 2rem;
          border-radius: 50px;
          font-size: 1.1rem;
          font-weight: 600;
          text-decoration: none;
          display: inline-flex;
          align-items: center;
          gap: 0.75rem;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          cursor: pointer;
          backdrop-filter: blur(10px);
          box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
          position: relative;
          z-index: 5;
          opacity: 1;
          visibility: visible;
        }

        .product-nav-btn:hover {
          background: linear-gradient(135deg, #002956 0%, #00a0e9 100%);
          border-color: #00a0e9;
          color: white;
          transform: translateY(-3px);
          box-shadow: 0 8px 25px rgba(0, 160, 233, 0.3);
          text-decoration: none;
        }

        .product-nav-btn .icon {
          font-size: 1.2rem;
        }

        /* Ensure buttons are always visible as fallback */
        .products-nav-buttons button {
          opacity: 1 !important;
          visibility: visible !important;
        }

        /* Enhanced button container visibility */
        .products-nav-buttons {
          padding: 1rem;
          border-radius: 10px;
        }

        /* Section Spacing */
        .product-section {
          scroll-margin-top: 80px;
          position: relative;
        }

        /* Add subtle separation between product sections */
        .product-section:not(:last-child)::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 100px;
          height: 2px;
          background: linear-gradient(90deg, transparent 0%, #00a0e9 50%, transparent 100%);
          opacity: 0.3;
        }

        /* Smooth scroll behavior */
        html {
          scroll-behavior: smooth;
        }

        /* Enhanced scroll padding for better positioning */
        @media (max-width: 768px) {
          .product-section {
            scroll-margin-top: 60px;
          }
        }

        /* Scroll to Top Button */
        .scroll-to-top {
          position: fixed;
          bottom: 2rem;
          right: 2rem;
          width: 50px;
          height: 50px;
          background: linear-gradient(135deg, #002956 0%, #00a0e9 100%);
          border: none;
          border-radius: 50%;
          color: white;
          font-size: 1.2rem;
          cursor: pointer;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          z-index: 1000;
          box-shadow: 0 4px 15px rgba(0, 160, 233, 0.3);
          opacity: 0;
          visibility: hidden;
          transform: translateY(20px);
        }

        .scroll-to-top.visible {
          opacity: 1;
          visibility: visible;
          transform: translateY(0);
        }

        .scroll-to-top:hover {
          transform: translateY(-3px);
          box-shadow: 0 6px 20px rgba(0, 160, 233, 0.4);
        }

        @media (max-width: 768px) {
          .scroll-to-top {
            bottom: 1.5rem;
            right: 1.5rem;
            width: 45px;
            height: 45px;
            font-size: 1.1rem;
          }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
          .products-nav-buttons {
            flex-direction: column;
            align-items: center;
            gap: 1rem;
          }

          .product-nav-btn {
            width: 100%;
            max-width: 300px;
            justify-content: center;
            font-size: 1rem;
            padding: 0.875rem 1.5rem;
          }

          .products-hero-subtitle {
            font-size: 1.1rem;
            margin-bottom: 2rem;
          }
        }

        @media (max-width: 480px) {
          .products-hero {
            min-height: 80vh;
          }

          .product-nav-btn {
            padding: 0.75rem 1.25rem;
            font-size: 0.95rem;
          }
        }
      `}</style>

      {/* Hero Section */}
      <section ref={heroRef} className="products-hero">
        <Container>
          <div className="products-hero-content">
            <h1 ref={titleRef} className="products-hero-title">
              Our Products
            </h1>
            <p ref={subtitleRef} className="products-hero-subtitle">
              Discover our comprehensive suite of innovative solutions designed to transform your business operations. 
              From advanced recruitment systems to cutting-edge trading platforms, explore how Makonis products 
              drive efficiency, intelligence, and growth across industries.
            </p>
            
            {/* Navigation Buttons */}
            <div ref={buttonsRef} className="products-nav-buttons" style={{ opacity: 1, visibility: 'visible' }}>
              <button
                className="product-nav-btn"
                onClick={() => scrollToSection(atsDemoRef)}
                style={{ opacity: 1, visibility: 'visible' }}
              >
                <FaRocket className="icon" />
                ATS-Talent Track Pro
              </button>
              <button
                className="product-nav-btn"
                onClick={() => scrollToSection(makoPlusRef)}
                style={{ opacity: 1, visibility: 'visible' }}
              >
                <FaShieldAlt className="icon" />
                MakoPlus
              </button>
              <button
                className="product-nav-btn"
                onClick={() => scrollToSection(tradingIntelligenceRef)}
                style={{ opacity: 1, visibility: 'visible' }}
              >
                <FaChartLine className="icon" />
                Trading Intelligence
              </button>
            </div>
          </div>
        </Container>
      </section>

      {/* Product Sections */}
      <div ref={atsDemoRef} className="product-section">
        <ATSDemo />
      </div>

      <div ref={makoPlusRef} className="product-section">
        <MakoPlus />
      </div>

      <div ref={tradingIntelligenceRef} className="product-section">
        <TradingIntelligence />
      </div>

      {/* Scroll to Top Button */}
      <button
        className={`scroll-to-top ${showScrollTop ? 'visible' : ''}`}
        onClick={scrollToTop}
        aria-label="Scroll to top"
      >
        <FaArrowUp />
      </button>
    </>
  );
};

export default ProductsPage;
