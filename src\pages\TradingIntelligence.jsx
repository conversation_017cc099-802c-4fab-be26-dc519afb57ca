import React, { useRef, useState, useEffect } from 'react';
import { Container, Row, Col } from 'react-bootstrap';
import {
  FaPlay, FaPause, FaVolumeUp, FaVolumeOff, FaExpand, FaUsers,
  FaChartLine, FaRobot, FaFastBackward, FaFastForward, FaFileMedical,
  FaChartBar, FaShieldAlt, FaMobileAlt
} from 'react-icons/fa';
import MTIVideo from '../assets/MTI.mp4';
import MTI10 from '../assets/MTI10.avif';

const TradingIntelligence = () => {
  // Refs for video player and hero section elements
  const videoRef = useRef(null);
  const heroRef = useRef(null);
  const titleRef = useRef(null);
  const subtitleRef = useRef(null);

  // State for video player
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [isMuted, setIsMuted] = useState(false);
  const [isVideoLoading, setIsVideoLoading] = useState(true);
  const [videoError, setVideoError] = useState(null);

  const features = [
    { icon: FaFileMedical, title: "AI-Powered Analytics", description: "Advanced machine learning predicts trading opportunities." },
    { icon: FaChartBar, title: "Real-Time Market Intelligence", description: "Get instant market insights with real-time data processing." },
    { icon: FaShieldAlt, title: "Automated Trading Strategies", description: "Deploy automated trading strategies that adapt and optimize." },
    { icon: FaMobileAlt, title: "Risk Management", description: "Risk management tools to protect investments and maximize returns." }
  ];

  const demoKeyPoints = [
    { icon: FaUsers, text: "AI-Powered Analytics" },
    { icon: FaChartLine, text: "Real-time Market Data" },
    { icon: FaRobot, text: "Automated Trading" },
    { icon: FaShieldAlt, text: "Risk Management" }
  ];

  const benefits = [
    "Advanced algorithmic trading", "Real-time market data", "Customizable dashboards",
    "Risk management & compliance", "Multi-asset class support", "API integrations",
    "Real-time market insights", "Advanced risk tools"
  ];

  // Video player handlers (No changes needed here)
  const handlePlayPause = async () => {
    if (videoRef.current) {
      if (isPlaying) { videoRef.current.pause(); }
      else { try { await videoRef.current.play(); } catch (error) { console.error("Video play failed:", error); setVideoError("Playback was prevented."); } }
    }
  };
  const handleTimeUpdate = () => videoRef.current && setCurrentTime(videoRef.current.currentTime);
  const handleLoadedMetadata = () => { if (videoRef.current) { setDuration(videoRef.current.duration); setIsVideoLoading(false); } };
  const handleProgressClick = (e) => {
    if (videoRef.current) {
      const progressBar = e.currentTarget;
      const clickX = e.nativeEvent.offsetX;
      const newTime = (clickX / progressBar.offsetWidth) * duration;
      videoRef.current.currentTime = newTime;
      setCurrentTime(newTime);
    }
  };
  const formatTime = (time) => {
    if (isNaN(time) || time === 0) return '00:00';
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };
  const handleVolumeToggle = () => { if (videoRef.current) { videoRef.current.muted = !videoRef.current.muted; setIsMuted(videoRef.current.muted); } };
  const handleFullscreen = () => {
    const videoElement = videoRef.current;
    if (!videoElement) return;
    if (videoElement.requestFullscreen) videoElement.requestFullscreen();
    else if (videoElement.mozRequestFullScreen) videoElement.mozRequestFullScreen();
    else if (videoElement.webkitRequestFullscreen) videoElement.webkitRequestFullscreen();
    else if (videoElement.msRequestFullscreen) videoElement.msRequestFullscreen();
  };
  const handleSkipForward = () => videoRef.current && (videoRef.current.currentTime += 15);
  const handleSkipBackward = () => videoRef.current && (videoRef.current.currentTime -= 15);
  const handleCanPlay = () => { setIsVideoLoading(false); setVideoError(null); };
  const handleVideoError = (e) => { setVideoError(`Error loading video. Code: ${e.target.error?.code}`); setIsVideoLoading(false); };

  // Simplified Intersection Observer for animations
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add('is-visible');
            observer.unobserve(entry.target);
          }
        });
      }, { threshold: 0.1 }
    );

    const elements = document.querySelectorAll('.animate-on-scroll');
    elements.forEach(el => observer.observe(el));

    return () => elements.forEach(el => {
      if (el) { observer.unobserve(el); }
    });
  }, []);

  return (
    <div
      className="trading-intelligence-page min-h-screen relative overflow-hidden"
      style={{
        background: "linear-gradient(135deg, rgba(0, 41, 86, 0.95) 0%, rgba(0, 41, 86, 0.98) 100%)",
        backdropFilter: "blur(10px)",
      }}
    >
      {/* Background Grid Pattern */}
      <div className="absolute inset-0 z-0">
        <div
          className="absolute inset-0 opacity-20"
          style={{
            backgroundImage: `linear-gradient(rgba(0, 160, 233, 0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(0, 160, 233, 0.1) 1px, transparent 1px)`,
            backgroundSize: "50px 50px",
          }}
        />
        {/* Animated Background Icons */}
        {[
          // {
          //   icon: "fa-chart-line",
          //   top: "10%",
          //   left: "5%",
          //   delay: 0,
          // },
          // {
          //   icon: "fa-brain",
          //   top: "20%",
          //   right: "8%",
          //   delay: 1,
          // },
          // {
          //   icon: "fa-robot",
          //   bottom: "15%",
          //   left: "3%",
          //   delay: 2,
          // },
          // {
          //   icon: "fa-shield-alt",
          //   bottom: "25%",
          //   right: "10%",
          //   delay: 3,
          // },
        ].map((item, index) => (
          <div
            key={index}
            className="position-absolute"
            style={{
              top: item.top,
              left: item.left,
              right: item.right,
              bottom: item.bottom,
              animation: `float 6s ease-in-out infinite`,
              animationDelay: `${item.delay}s`,
              opacity: 0.1,
            }}
          >
            <i
              className={`fas ${item.icon} text-white`}
              style={{ fontSize: "2rem" }}
            ></i>
          </div>
        ))}
      </div>
      <div className="relative z-10">
      <style jsx>{`
        /* --- Global Styles & Animations --- */
        .trading-intelligence-page {
            overflow-x: hidden;
            font-family: 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            color: #f1f5f9;
        }
        .animate-on-scroll {
            opacity: 0;
            transform: translateY(40px);
            transition: opacity 0.8s ease-out, transform 0.8s ease-out;
        }
        .animate-on-scroll.is-visible {
            opacity: 1;
            transform: translateY(0);
        }

        /* --- Trading Intelligence Hero Section --- */
        .trading-hero-section {
          position: relative;
          min-height: 100vh;
          display: flex;
          align-items: center;
          justify-content: center; /* ✅ Center content horizontally */
          overflow: hidden;
          padding: 2rem 1rem;
          text-align: center; /* ✅ Center text */
          /* ✅ Added background image with overlay */
          background: linear-gradient(rgba(1, 26, 58, 0.85), rgba(1, 26, 58, 0.85)),   url(${MTI10}) center/cover no-repeat;
        }
        .trading-hero-title {
          font-size: clamp(1.875rem, 5vw, 3rem);
          font-weight: 800;
          line-height: 1.15;
          margin-bottom: 1.5rem;
          background: linear-gradient(135deg, #ffffff 40%, #00a0e9 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
          text-shadow: 0 0 40px rgba(0, 160, 233, 0.4);
        }
        .trading-hero-subtitle {
          font-size: clamp(1.4rem, 2.5vw, 1.25rem);
          color: rgba(255, 255, 255, 0.85);
          max-width: 800px; /* Increased max-width for centered layout */
          margin: 0 auto 2.5rem auto;
          line-height: 1.7;
        }
        
        /* --- Shared Content Section Styles --- */
        .content-section {
          padding: 2rem 1rem;
        }
        .section-title {
            font-size: clamp(1.5rem, 4vw, 2.25rem);
            font-weight: 800;
            margin-bottom: 1rem;
            text-align: center;
            background: linear-gradient(135deg, #ffffff 40%, #00a0e9 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 30px rgba(0, 160, 233, 0.3);
        }
        .section-subtitle {
            font-size: 1.1rem;
            color: rgba(241, 245, 249, 0.8);
            max-width: 800px;
            margin: 0 auto 3rem auto;
            line-height: 1.7;
            text-align: center;
        }
        
        /* --- Demo Section --- */
        .demo-key-points-grid {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 1.5rem;
        }
        .demo-key-point {
            background: linear-gradient(135deg, #00a0e9 0%, #0056b3 100%);
            border-radius: 0.75rem;
            padding: 1.5rem;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.75rem;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
            flex-basis: 220px;
            text-align: center;
        }
        .demo-key-point:hover {
            transform: translateY(-8px);
            box-shadow: 0 10px 25px rgba(0, 160, 233, 0.3);
        }
        .demo-key-point :global(.icon) { font-size: 2rem; margin-bottom: 0.5rem; }

        /* --- Video & Features Section --- */
        .video-container {
            background: #000;
            width: 100%;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            position: relative;
            overflow: hidden;
            border-radius: 1rem;
            aspect-ratio: 16 / 9;
        }
        .video-container video { width: 100%; height: 100%; object-fit: cover; }
        .feature-list-container {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
            height: 100%;
            justify-content: center;
        }

        /* Matched glass style from other components */
        .feature-card, .benefit-box {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            transition: all 0.4s ease;
            padding: 1.5rem;
            border-radius: 1rem;
        }
        .feature-card:hover, .benefit-box:hover {
            transform: translateY(-8px);
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(0, 160, 233, 0.5);
        }
        .feature-card {
            display: flex;
            align-items: flex-start;
            gap: 1.25rem;
        }
        .benefit-box {
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
        }
        
        .feature-icon {
            width: 55px; height: 55px; min-width: 55px;
            border-radius: 0.5rem;
            display: flex; align-items: center; justify-content: center;
            background: linear-gradient(135deg, #00a0e9 0%, #0056b3 100%);
            color: white;
            font-size: 25px;
        }
        .feature-card h5 {
            color: #f1f5f9; font-size: 1.1rem;
            font-weight: 700; margin-bottom: 0.5rem;
        }
        .feature-card p {
            color: rgba(241, 245, 249, 0.8); font-size: 0.95rem;
            line-height: 1.6; margin: 0;
        }
        .benefit-box h6 {
            font-size: 1rem; font-weight: 600;
            color: #f1f5f9; margin: 0;
        }
        
        /* Video Player Styles */
        .video-overlay {
            position: absolute; top: 0; left: 0; width: 100%; height: 100%;
            display: flex; align-items: center; justify-content: center;
            background-color: rgba(0,0,0,0.5); z-index: 5;
            transition: opacity 0.3s ease;
        }
        @keyframes playButtonPulse { 50% { box-shadow: 0 0 0 15px rgba(0, 160, 233, 0.3); } }
        .play-button {
            background: linear-gradient(135deg, rgba(0, 160, 233, 0.9) 0%, rgba(0, 86, 179, 0.9) 100%);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.3);
            width: 90px; height: 90px;
            border-radius: 50%; cursor: pointer;
            display: flex; align-items: center; justify-content: center;
            animation: playButtonPulse 3s ease-in-out infinite;
            transition: transform 0.3s;
        }
        .play-button:hover { transform: scale(1.1); }
        .video-controls {
            position: absolute; bottom: 0; left: 0; right: 0;
            opacity: 0; transform: translateY(100%);
            transition: all 0.4s ease-out;
            pointer-events: none;
            background: linear-gradient(transparent, rgba(0,0,0,0.8));
            padding: 0.75rem 1rem; z-index: 10;
        }
        .video-container:hover .video-controls { opacity: 1; transform: translateY(0); pointer-events: all; }
        .video-controls .btn { background: transparent; border: none; color: white; font-size: 1.1rem; padding: 0.5rem; }
        .video-controls .btn:hover { color: #00a0e9; }
        .progress-bar-container {
            flex-grow: 1; height: 8px;
            background-color: rgba(255,255,255,0.3);
            border-radius: 4px; cursor: pointer; margin: 0 1rem;
        }
        .progress-bar { height: 100%; background-color: #00a0e9; border-radius: 4px; }
        .time-display { font-size: 0.9rem; color: white; min-width: 90px; text-align: center; }
      `}</style>
      
      {/* Hero Section */}
      <section ref={heroRef} className="trading-hero-section">
        <Container>
            <div className="animate-on-scroll is-visible">
                <h1 ref={titleRef} className="trading-hero-title pb-2">
                    Makonis Trading Intelligence
                </h1>
                <p ref={subtitleRef} className="trading-hero-subtitle">
                    Revolutionize your trading with our AI-powered platform. Harness advanced analytics, real-time insights, and automated strategies to maximize performance and stay ahead of market movements.
                </p>
            </div>
        </Container>
      </section>

      {/* Live MTI Platform Demonstration Section */}
      <section className="content-section" id="demo">
        <Container>
          <h2 className="section-title animate-on-scroll">Live Platform Demonstration</h2>
          <p className="section-subtitle animate-on-scroll" style={{transitionDelay: '0.2s'}}>
            Discover the power of our advanced trading intelligence platform. This demo showcases real-time market analysis, AI-powered trading strategies, and risk management tools that drive superior trading performance.
          </p>
          <div className="demo-key-points-grid">
            {demoKeyPoints.map((point, index) => (
              <div key={index} className="demo-key-point animate-on-scroll" style={{transitionDelay: `${0.3 + index * 0.1}s`}}>
                <point.icon className="icon" />
                <span>{point.text}</span>
              </div>
            ))}
          </div>
        </Container>
      </section>

      {/* Video and Features Section */}
      <section className="content-section">
        <Container>
          <Row className="align-items-center">
            <Col lg={7} className="mb-4 mb-lg-0 animate-on-scroll">
              <div className="video-container">
                <video ref={videoRef} onTimeUpdate={handleTimeUpdate} onLoadedMetadata={handleLoadedMetadata} onCanPlay={handleCanPlay} onError={handleVideoError} onPlay={() => setIsPlaying(true)} onPause={() => setIsPlaying(false)} preload="metadata" playsInline>
                  <source src={MTIVideo} type="video/mp4" />
                  Your browser does not support the video tag.
                </video>
                {isVideoLoading && <div className="video-overlay"><div className="spinner-border text-primary" role="status"></div></div>}
                {videoError && <div className="video-overlay"><p className="text-white bg-danger p-3 rounded">{videoError}</p></div>}
                {!isPlaying && !isVideoLoading && !videoError && (
                  <div className="video-overlay" onClick={handlePlayPause}>
                    <div className="play-button"><FaPlay size={30} color="white" style={{marginLeft: '4px'}} /></div>
                  </div>
                )}
                <div className="video-controls d-flex align-items-center">
                    <button className="btn" onClick={handlePlayPause} aria-label={isPlaying ? 'Pause' : 'Play'}>{isPlaying ? <FaPause /> : <FaPlay />}</button>
                    <button className="btn" onClick={handleSkipBackward} aria-label="Skip Backward"><FaFastBackward /></button>
                    <button className="btn" onClick={handleSkipForward} aria-label="Skip Forward"><FaFastForward /></button>
                    <div className="progress-bar-container" onClick={handleProgressClick}>
                        <div className="progress-bar" style={{ width: `${(currentTime / duration) * 100}%` }}></div>
                    </div>
                    <div className="time-display">{formatTime(currentTime)} / {formatTime(duration)}</div>
                    <button className="btn" onClick={handleVolumeToggle} aria-label={isMuted ? 'Unmute' : 'Mute'}>{isMuted ? <FaVolumeOff /> : <FaVolumeUp />}</button>
                    <button className="btn" onClick={handleFullscreen} aria-label="Fullscreen"><FaExpand /></button>
                </div>
              </div>
            </Col>
            <Col lg={5}>
              <div className="feature-list-container">
                {features.map((feature, index) => (
                  <div key={index} className="feature-card animate-on-scroll" style={{transitionDelay: `${0.2 + index * 0.1}s`}}>
                    <div className="feature-icon"><feature.icon /></div>
                    <div>
                      <h5>{feature.title}</h5>
                      <p>{feature.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </Col>
          </Row>
        </Container>
      </section>

      {/* Benefits Section */}
      <section className="content-section">
        <Container>
          <h2 className="section-title animate-on-scroll pb-3">Why Choose Our Trading Intelligence?</h2>
          <Row className="mt-4">
            {benefits.map((benefit, index) => (
              <Col md={6} lg={3} key={index} className="mb-4">
                <div className="benefit-box animate-on-scroll" style={{transitionDelay: `${0.2 + index * 0.05}s`}}>
                  <h6>{benefit}</h6>
                </div>
              </Col>
            ))}
          </Row>
        </Container>
      </section>
      </div>
      <style>{`
        @keyframes float {
          0%, 100% {
            transform: translateY(0px);
          }
          50% {
            transform: translateY(-20px);
          }
        }
      `}</style>
    </div>
  );
};

export default TradingIntelligence;