import { useEffect, useState, useRef } from "react";
import { Link } from "react-router-dom";
import { Container, Row, Col, Image, ListGroup } from "react-bootstrap";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import "animate.css";
import Contact1 from "../assets/Contact1.png";
import Contact2 from "../assets/Contact2.png";
import Contact3 from "../assets/Contact3.png";

gsap.registerPlugin(ScrollTrigger);

const permanentHireData = {
  hero: {
    title: "Permanent Hire Staffing Services",
    subtitle:
      "Strategic Permanent Placement for Sustainable Growth. We specialize in finding exceptional full-time professionals who will become integral parts of your organization and contribute to long-term success.",
    backgroundImage:
      "https://images.unsplash.com/photo-1521737711867-e3b97375f902?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1926&q=80",
  },
  sections: [
    {
      id: "benefits",
      title: "Benefits of Permanent Hire Staffing",
      texts: [""],
      listItems: [
        "Long-Term Stability: Build a committed workforce with employees invested in your company's future and growth trajectory.",
        "Cultural Integration: Comprehensive assessment ensures new hires align with your organizational values and team dynamics.",
        "Reduced Recruitment Burden: Our full-service approach handles everything from sourcing to onboarding, saving you time and resources.",
        "Higher ROI: Permanent employees provide better return on investment through increased productivity and reduced turnover costs.",
        "Skill Development: Permanent staff can grow with your organization, developing specialized skills and institutional knowledge.",
        "Team Cohesion: Stable workforce promotes better collaboration, communication, and overall team performance.",
      ],
      image: Contact1,
      alt: "Benefits of permanent hire staffing services",
      reversed: false,
    },
    {
      id: "process",
      title: "Our Permanent Hire Process",
      texts: [""],
      listItems: [
        "Strategic Planning: Comprehensive analysis of your long-term staffing needs and organizational goals.",
        "Role Definition: Detailed job analysis including responsibilities, required skills, and growth opportunities.",
        "Talent Acquisition: Proactive sourcing from our network of qualified professionals and industry connections.",
        "Multi-Stage Evaluation: Rigorous screening including technical assessments, behavioral interviews, and reference checks.",
        "Cultural Assessment: Evaluation of candidate fit with your company culture and team dynamics.",
        "Seamless Integration: Support throughout the hiring process and initial onboarding to ensure successful placement.",
      ],
      image: Contact2,
      alt: "Permanent hire staffing process",
      reversed: true,
    },
    {
      id: "why-work-with-us",
      title: "Why Choose Our Permanent Hire Services?",
      texts: [""],
      listItems: [
        "Extensive Network: Access to millions of qualified candidates across all industries and experience levels.",
        "Industry Expertise: Specialized knowledge in IT, healthcare, finance, engineering, manufacturing, and professional services.",
        "Proven Methodology: Time-tested recruitment process with high success rates and client satisfaction.",
        "Quality Guarantee: Comprehensive screening and evaluation process ensures only the best candidates are presented.",
        "Ongoing Support: Continued relationship management and support even after successful placement.",
        "Competitive Advantage: Help you attract top talent in competitive markets with our industry reputation and reach.",
      ],
      image: Contact3,
      alt: "Why choose our permanent hire services",
      reversed: false,
    },
  ],
};

const PermanentHireStaffingPage = () => {
  const heroRef = useRef(null);
  const sectionsRef = useRef([]);

  useEffect(() => {
    window.scrollTo(0, 0);

    // Hero animation
    if (heroRef.current) {
      gsap.fromTo(
        heroRef.current.querySelectorAll(".hero-content > *"),
        { opacity: 0, y: 50 },
        {
          opacity: 1,
          y: 0,
          duration: 1,
          stagger: 0.2,
          ease: "power3.out",
        }
      );
    }

    // Section animations
    sectionsRef.current.forEach((section, index) => {
      if (section) {
        gsap.fromTo(
          section.querySelectorAll(".animate-in"),
          { opacity: 0, y: 30 },
          {
            opacity: 1,
            y: 0,
            duration: 0.8,
            stagger: 0.1,
            ease: "power2.out",
            scrollTrigger: {
              trigger: section,
              start: "top 80%",
              end: "bottom 20%",
              toggleActions: "play none none reverse",
            },
          }
        );
      }
    });

    return () => {
      ScrollTrigger.getAll().forEach((trigger) => trigger.kill());
    };
  }, []);

  const addToRefs = (el) => {
    if (el && !sectionsRef.current.includes(el)) {
      sectionsRef.current.push(el);
    }
  };

  return (
    <div className="permanent-hire-page">
      {/* Hero Section */}
      <section
        ref={heroRef}
        className="hero-section position-relative d-flex align-items-center justify-content-center text-center text-white overflow-hidden"
        style={{
          minHeight: "100vh",
          background: `linear-gradient(135deg, rgba(0, 41, 86, 0.9) 0%, rgba(0, 160, 233, 0.8) 100%), url(${permanentHireData.hero.backgroundImage})`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundAttachment: "fixed",
        }}
      >
        {/* Animated Background Elements */}
        <div className="position-absolute w-100 h-100">
          <div
            className="position-absolute"
            style={{
              top: "10%",
              left: "10%",
              width: "100px",
              height: "100px",
              background:
                "linear-gradient(45deg, rgba(255,255,255,0.1), rgba(0,160,233,0.2))",
              borderRadius: "50%",
              animation: "float 6s ease-in-out infinite",
            }}
          />
          <div
            className="position-absolute"
            style={{
              top: "60%",
              right: "15%",
              width: "150px",
              height: "150px",
              background:
                "linear-gradient(45deg, rgba(255,255,255,0.05), rgba(0,160,233,0.15))",
              borderRadius: "50%",
              animation: "float 8s ease-in-out infinite reverse",
            }}
          />
        </div>

        <Container className="position-relative z-index-2">
          <div className="hero-content">
            <h1
              className="display-3 fw-bold mb-4"
              style={{
                fontSize: "clamp(2.5rem, 6vw, 4rem)",
                lineHeight: "1.2",
                background:
                  "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
                WebkitBackgroundClip: "text",
                WebkitTextFillColor: "transparent",
                backgroundClip: "text",
                textShadow: "0 0 40px rgba(0, 160, 233, 0.4)",
              }}
            >
              {permanentHireData.hero.title}
            </h1>
            <p
              className="lead mb-5 mx-auto"
              style={{
                maxWidth: "800px",
                fontSize: "clamp(1.1rem, 2.5vw, 1.4rem)",
                lineHeight: "1.6",
                color: "rgba(255, 255, 255, 0.9)",
              }}
            >
              {permanentHireData.hero.subtitle}
            </p>
            <div className="d-flex flex-column flex-sm-row gap-3 justify-content-center">
              <Link
                to="/contact"
                className="btn btn-lg px-5 py-3 rounded-pill fw-semibold text-decoration-none"
                style={{
                  background: "linear-gradient(135deg, #00a0e9 0%, #002956 100%)",
                  border: "none",
                  color: "white",
                  boxShadow: "0 8px 25px rgba(0, 160, 233, 0.3)",
                  transition: "all 0.3s ease",
                }}
                onMouseEnter={(e) => {
                  e.target.style.transform = "translateY(-2px)";
                  e.target.style.boxShadow = "0 12px 35px rgba(0, 160, 233, 0.4)";
                }}
                onMouseLeave={(e) => {
                  e.target.style.transform = "translateY(0)";
                  e.target.style.boxShadow = "0 8px 25px rgba(0, 160, 233, 0.3)";
                }}
              >
                Get Started Today
              </Link>
              <Link
                to="/services"
                className="btn btn-outline-light btn-lg px-5 py-3 rounded-pill fw-semibold text-decoration-none"
                style={{
                  borderWidth: "2px",
                  transition: "all 0.3s ease",
                }}
                onMouseEnter={(e) => {
                  e.target.style.background = "rgba(255, 255, 255, 0.1)";
                  e.target.style.transform = "translateY(-2px)";
                }}
                onMouseLeave={(e) => {
                  e.target.style.background = "transparent";
                  e.target.style.transform = "translateY(0)";
                }}
              >
                Learn More
              </Link>
            </div>
          </div>
        </Container>
      </section>

      {/* Main Content Sections */}
      <div
        className="main-content"
        style={{
          background: "linear-gradient(135deg, #002956 0%, #001a35 50%, #002956 100%)",
          minHeight: "100vh",
        }}
      >
        <Container>
          {permanentHireData.sections.map((section, idx) => (
            <section
              key={section.id}
              ref={addToRefs}
              className="mb-5 mb-md-6 py-3"
            >
              {/* Centered Heading Above Content */}
              <Row>
                <Col xs={12}>
                  <h2
                    className="text-center"
                    style={{
                      fontSize: "clamp(1.5rem, 4vw, 2.25rem)",
                      fontWeight: "800",
                      letterSpacing: "2.6px",
                      paddingBottom: "1.2rem",
                      background:
                        "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
                      WebkitBackgroundClip: "text",
                      WebkitTextFillColor: "transparent",
                      backgroundClip: "text",
                      textShadow: "0 0 30px rgba(0, 160, 233, 0.3)",
                    }}
                  >
                    {section.title}
                  </h2>
                  {/* Enhanced Accent Line */}
                  <div className="w-30 h-1 mx-auto relative mb-5">
                    <div
                      className="w-full h-full rounded-sm shadow-glow"
                      style={{
                        background:
                          "linear-gradient(90deg, transparent, #00a0e9, transparent)",
                      }}
                    />
                  </div>
                </Col>
              </Row>

              {/* Content and Image Row */}
              <Row
                className={`align-items-center g-4 g-lg-5 ${
                  section.reversed ? "flex-row-reverse" : ""
                }`}
              >
                <Col
                  lg={6}
                  md={12}
                  className={`${section.reversed ? "ps-lg-4" : "pe-lg-4"}`}
                >
                  <div className="content-wrapper">
                    {section.texts.map((text, textIdx) => (
                      <p
                        key={textIdx}
                        className="mb-3 mb-md-4 animate-in"
                        style={{
                          fontSize: "1.2rem",
                          lineHeight: "1.7",
                          color: "rgba(255, 255, 255, 0.9)",
                        }}
                      >
                        {text}
                      </p>
                    ))}

                    {section.listItems && (
                      <ListGroup
                        variant="flush"
                        className="bg-transparent animate-in"
                      >
                        {section.listItems.map((item, itemIdx) => (
                          <ListGroup.Item
                            key={itemIdx}
                            className="bg-transparent border-0 px-0 py-2"
                            style={{
                              color: "rgba(255, 255, 255, 0.85)",
                              fontSize: "1.1rem",
                              lineHeight: "1.6",
                            }}
                          >
                            <div className="d-flex align-items-start">
                              <span
                                className="me-3 mt-1 flex-shrink-0"
                                style={{
                                  width: "8px",
                                  height: "8px",
                                  background: "#00a0e9",
                                  borderRadius: "50%",
                                  display: "inline-block",
                                }}
                              />
                              <span>{item}</span>
                            </div>
                          </ListGroup.Item>
                        ))}
                      </ListGroup>
                    )}
                  </div>
                </Col>

                <Col lg={6} md={12} className="text-center">
                  <div
                    className="image-wrapper animate-in"
                    style={{
                      borderRadius: "20px",
                      overflow: "hidden",
                      boxShadow: "0 20px 60px rgba(0, 160, 233, 0.2)",
                      background:
                        "linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(0,160,233,0.1) 100%)",
                      padding: "2px",
                    }}
                  >
                    <Image
                      src={section.image}
                      alt={section.alt}
                      fluid
                      style={{
                        borderRadius: "18px",
                        width: "100%",
                        height: "auto",
                        objectFit: "cover",
                      }}
                    />
                  </div>
                </Col>
              </Row>
            </section>
          ))}
        </Container>
      </div>

      {/* Call to Action Section */}
      <section
        className="cta-section py-5"
        style={{
          background: "linear-gradient(135deg, #00a0e9 0%, #002956 100%)",
          color: "white",
        }}
      >
        <Container>
          <Row className="text-center">
            <Col lg={8} className="mx-auto">
              <h2
                className="display-5 fw-bold mb-4"
                style={{
                  fontSize: "clamp(2rem, 5vw, 3rem)",
                  lineHeight: "1.2",
                }}
              >
                Ready to Secure Your Permanent Workforce?
              </h2>
              <p
                className="lead mb-5"
                style={{
                  fontSize: "clamp(1.1rem, 2.5vw, 1.3rem)",
                  opacity: "0.9",
                }}
              >
                Partner with us to find exceptional permanent employees who will become the foundation of your organization's success.
              </p>
              <div className="d-flex flex-column flex-sm-row gap-3 justify-content-center">
                <Link
                  to="/contact"
                  className="btn btn-light btn-lg px-5 py-3 rounded-pill fw-semibold text-decoration-none"
                  style={{
                    color: "#002956",
                    boxShadow: "0 8px 25px rgba(255, 255, 255, 0.2)",
                    transition: "all 0.3s ease",
                  }}
                  onMouseEnter={(e) => {
                    e.target.style.transform = "translateY(-2px)";
                    e.target.style.boxShadow = "0 12px 35px rgba(255, 255, 255, 0.3)";
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.transform = "translateY(0)";
                    e.target.style.boxShadow = "0 8px 25px rgba(255, 255, 255, 0.2)";
                  }}
                >
                  Contact Us Today
                </Link>
                <Link
                  to="/services"
                  className="btn btn-outline-light btn-lg px-5 py-3 rounded-pill fw-semibold text-decoration-none"
                  style={{
                    borderWidth: "2px",
                    transition: "all 0.3s ease",
                  }}
                  onMouseEnter={(e) => {
                    e.target.style.background = "rgba(255, 255, 255, 0.1)";
                    e.target.style.transform = "translateY(-2px)";
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.background = "transparent";
                    e.target.style.transform = "translateY(0)";
                  }}
                >
                  View All Services
                </Link>
              </div>
            </Col>
          </Row>
        </Container>
      </section>

      {/* Floating Animation Styles */}
      <style>{`
        @keyframes float {
          0%, 100% {
            transform: translateY(0px);
          }
          50% {
            transform: translateY(-20px);
          }
        }

        .shadow-glow {
          box-shadow: 0 0 20px rgba(0, 160, 233, 0.5);
        }

        .z-index-2 {
          z-index: 2;
        }
      `}</style>
    </div>
  );
};

export default PermanentHireStaffingPage;
