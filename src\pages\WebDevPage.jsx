import React, { useEffect, useState, useRef } from "react";
import { <PERSON> } from "react-router-dom";
import { Con<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, But<PERSON>, Badge } from "react-bootstrap";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";


import web1 from "../assets/web1.jpg";
import web2 from "../assets/web2.jpg";
import web3 from "../assets/web3.jpg";
import web4 from "../assets/web4.jpg";
import web6 from "../assets/web6.jpg";

gsap.registerPlugin(ScrollTrigger);

// Define the page data structure
const webDevPageData = {
  hero: {
    title: "Web & Mobile Development",
    subtitle:
      "We combine in-depth industry expertise with world-class technical knowledge to help you create compelling software-based products that deliver exceptional user experiences. Our team supports you through every stage of development, ensuring quality, scalability, and innovation. Let us help turn your ideas into successful, market-ready solutions.",
    backgroundImage:
      "https://images.unsplash.com/photo-1547658719-da2b51169166?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
  },
  intro: {
    title: "Transforming Ideas into Digital Reality",
    description:
      "We have a plethora of web and mobile application services with enriched UI/UX that caters to all your business needs. Our esteemed programmers are always dedicated to delivering custom web application development that drives results.",
    image: web1,
  },
  services: [
    {
      id: "web-design",
      title: "Web Design",
      icon: "fa-palette",
      description:
        "Web 3.0 is designed to be a 'smarter web' resulting from the amalgamation of content, social, business, and community. We create interactive, shareable, and connected websites that help prospective clients find relevant information.",
      features: [
        "User Experience & User Interface Design (UI/UX)",
        "High quality responsive designs",
        "Multi-device friendly layouts",
        "Fast loading and SEO friendly",
      ],
      image: web2,
      color: "#ffffff",
    },
    {
      id: "ecommerce",
      title: "E-commerce",
      icon: "fa-shopping-cart",
      description:
        "There are many factors that make an e-commerce site user-friendly, from information loading to fast checkout processes. Everything should be dynamic, responsive, and fast. We aim for robust, easy-to-handle websites with all the features you need.",
      features: [
        "Secure payment gateways",
        "Inventory management",
        "Mobile-optimized shopping experience",
        "Customer account management",
      ],
      image: web3,
      color: "#ffffff",
    },
    {
      id: "web-app",
      title: "Web Applications",
      icon: "fa-laptop-code",
      description:
        "Web-based applications communicate with users via HTTP and can range from light applications like calculators and calendars to intensive applications like word processors and spreadsheets. We develop robust, scalable web applications tailored to your business needs.",
      features: [
        "Progressive Web Apps (PWAs)",
        "Single Page Applications (SPAs)",
        "Custom business applications",
        "Cloud-based solutions",
      ],
      image: web4,
      color: "#ffffff",
    },
    {
      id: "digital-marketing",
      title: "Digital Marketing",
      icon: "fa-bullhorn",
      description:
        "Digital marketing is essential in today's fast-paced digital world. From online advertising to social media management, we ensure your digital presence is optimized for maximum impact and engagement with your target audience.",
      features: [
        "Search Engine Optimization (SEO)",
        "Social Media Marketing",
        "Content Marketing",
        "Email Marketing Campaigns",
      ],
      image: web6,
      color: "#ffffff",
    },
  ],
  technologies: {
    title: "Technologies We Use",
    description:
      "We leverage the latest technologies to build robust, scalable, and future-proof applications.",
    techs: [
      { name: "React", icon: "fab fa-react" },
      { name: "Angular", icon: "fab fa-angular" },
      { name: "Vue.js", icon: "fab fa-vuejs" },
      { name: "Node.js", icon: "fab fa-node-js" },
      { name: "PHP", icon: "fab fa-php" },
      { name: "WordPress", icon: "fab fa-wordpress" },
      { name: "HTML5", icon: "fab fa-html5" },
      { name: "CSS3", icon: "fab fa-css3-alt" },
      { name: "JavaScript", icon: "fab fa-js" },
      { name: "Python", icon: "fab fa-python" },
      { name: "Swift", icon: "fab fa-swift" },
      { name: "Android", icon: "fab fa-android" },
    ],
  },
};

const WebDevPage = () => {
  const [activeService, setActiveService] = useState("web-design");
  const [animatedElements, setAnimatedElements] = useState({});
  const elementsRef = useRef({});
  const heroRef = useRef(null);
  const ctaRef = useRef(null);

  // Style constants - Updated to match Makonis brand colors
  const primaryColor = "#002956";
  const secondaryColor = "#00a0e9";
  const primaryRgb = "0, 41, 86";
  const secondaryRgb = "0, 160, 233";

  useEffect(() => {
    window.scrollTo(0, 0);

    // GSAP Animations
    const ctx = gsap.context(() => {
      // Hero section animations
      if (heroRef.current) {
        gsap.from(heroRef.current.querySelector("h1"), {
          y: 100,
          opacity: 0,
          duration: 1.2,
          ease: "power3.out",
        });

        gsap.from(heroRef.current.querySelector("p"), {
          y: 50,
          opacity: 0,
          duration: 1,
          ease: "power2.out",
          delay: 0.3,
        });

        gsap.from(heroRef.current.querySelectorAll(".btn"), {
          y: 30,
          opacity: 0,
          duration: 0.8,
          ease: "back.out(1.7)",
          delay: 0.6,
        });
      }

      // CTA section animation (if present)
      if (ctaRef.current) {
        gsap.from(ctaRef.current.querySelector("h2"), {
          y: 50,
          opacity: 0,
          duration: 1,
          ease: "power2.out",
          scrollTrigger: {
            trigger: ctaRef.current,
            start: "top 80%",
            end: "bottom 20%",
            toggleActions: "play none none reverse",
          },
        });
      }
    });

    return () => ctx.revert();
  }, []);

  // Set up intersection observer for animations
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setAnimatedElements((prev) => ({
              ...prev,
              [entry.target.id]: true,
            }));
          }
        });
      },
      { threshold: 0.1 } // Trigger when 10% of the element is visible
    );

    // Observe all elements with refs
    Object.keys(elementsRef.current).forEach((key) => {
      if (elementsRef.current[key]) {
        observer.observe(elementsRef.current[key]);
      }
    });

    return () => {
      Object.keys(elementsRef.current).forEach((key) => {
        if (elementsRef.current[key]) {
          observer.unobserve(elementsRef.current[key]);
        }
      });
    };
  }, []);

  const createRef = (id) => {
    if (!elementsRef.current[id]) {
      elementsRef.current[id] = React.createRef();
    }
    return elementsRef.current[id];
  };

  return (
    <div
      className="webdev-page overflow-hidden"
      style={{
        background:
          "linear-gradient(135deg, rgba(0, 41, 86, 0.95) 0%, rgba(0, 41, 86, 0.98) 100%)",
        backdropFilter: "blur(10px)",
      }}
    >
      {/* Background Grid Pattern */}
      <div className="absolute inset-0 z-0">
        <div
          className="absolute inset-0 opacity-20"
          style={{
            backgroundImage: `linear-gradient(rgba(0, 160, 233, 0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(0, 160, 233, 0.1) 1px, transparent 1px)`,
            backgroundSize: "50px 50px",
          }}
        />
      </div>

      {/* Hero Section */}
      <section
        ref={heroRef}
        className="webdev-hero-section text-white text-center d-flex align-items-center position-relative overflow-hidden"
        style={{
          backgroundImage: `linear-gradient(rgba(0, 41, 86, 0.85), rgba(0, 41, 86, 0.95)), url(${webDevPageData.hero.backgroundImage})`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          minHeight: "100vh",
          padding: "8rem 0",
        }}
      >
        {/* Animated Background Elements */}
        <div className="position-absolute w-100 h-100" style={{ zIndex: 1 }}>
          {/* Floating Web Dev Icons */}
          {[
            // { icon: "fa-code", top: "15%", left: "10%", delay: 0 },
            // { icon: "fa-laptop-code", top: "25%", right: "15%", delay: 1 },
            // { icon: "fa-mobile-alt", bottom: "20%", left: "8%", delay: 2 },
            // { icon: "fa-palette", bottom: "30%", right: "12%", delay: 3 },
          ].map((item, index) => (
            <div
              key={index}
              className="position-absolute"
              style={{
                ...item,
                width: "60px",
                height: "60px",
                background: "rgba(0, 160, 233, 0.1)",
                borderRadius: "50%",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                backdropFilter: "blur(10px)",
                border: "1px solid rgba(0, 160, 233, 0.2)",
                animation: `float 6s ease-in-out infinite`,
                animationDelay: `${item.delay}s`,
              }}
            >
              <i
                className={`fas ${item.icon}`}
                style={{
                  fontSize: "24px",
                  color: "#00a0e9",
                  textShadow: "0 0 10px rgba(0, 160, 233, 0.5)",
                }}
              ></i>
            </div>
          ))}

          {/* Pulse Circles */}
          {[
            // { size: "200px", top: "10%", right: "20%", delay: "0s" },
            // { size: "150px", bottom: "15%", left: "15%", delay: "2s" },
            // { size: "100px", top: "60%", right: "10%", delay: "4s" },
          ].map((circle, index) => (
            <div
              key={index}
              className="position-absolute rounded-circle"
              style={{
                width: circle.size,
                height: circle.size,
                top: circle.top,
                bottom: circle.bottom,
                left: circle.left,
                right: circle.right,
                background: "rgba(0, 160, 233, 0.05)",
                border: "1px solid rgba(0, 160, 233, 0.1)",
                animation: `pulse 4s ease-in-out infinite`,
                animationDelay: circle.delay,
              }}
            ></div>
          ))}
        </div>

        <Container className="position-relative" style={{ zIndex: 2 }}>
          <h1
            className="fw-bolder mb-4 animate__animated animate__fadeInDown animate__slow"
            style={{
              fontSize: "clamp(1.875rem, 5vw, 3rem)",
              fontWeight: "800",
              letterSpacing: "2.6px",
              background: "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              backgroundClip: "text",
              textShadow: "0 0 30px rgba(0, 160, 233, 0.3)",
            }}
          >
            {webDevPageData.hero.title}
          </h1>
          <p
            className="mb-5 mx-auto animate__animated animate__fadeInUp animate__slow"
            style={{
              maxWidth: "1200px",
              textShadow: "1px 1px 3px rgba(0,0,0,0.4)",
              fontSize: "clamp(1rem, 2.5vw, 1.35rem)",
              paddingLeft: "1.5rem",
              paddingRight: "1.5rem",
            }}
          >
            {webDevPageData.hero.subtitle}
          </p>
        </Container>
      </section>

      {/* Intro Section */}
      <section
        className="section-padding"
        id="intro-section"
        ref={(el) => (elementsRef.current["intro-section"] = el)}
      >
        <Container className="container-makonis">
          <Row className="align-items-center g-5">
            <Col
              lg={6}
              className={`mb-4 mb-lg-0 ${
                animatedElements["intro-section"]
                  ? "animate__animated animate__fadeInLeft"
                  : ""
              }`}
            >
              <h2
                style={{
                  fontSize: "3rem",
                  fontWeight: "800",
                  letterSpacing: "2.6px",
                  paddingBottom: "1.2rem",
                  background:
                    "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
                  WebkitBackgroundClip: "text",
                  WebkitTextFillColor: "transparent",
                  backgroundClip: "text",
                  textShadow: "0 0 30px rgba(0, 160, 233, 0.3)",
                }}
              >
                {webDevPageData.intro.title}
              </h2>
              <p
                className="lead mb-4 text-white"
                style={{ fontSize: "1.3rem", lineHeight: "1.5" }}
              >
                {webDevPageData.intro.description}
              </p>
              <div className="d-flex flex-wrap gap-3 mt-4"></div>
            </Col>
            <Col
              lg={6}
              className={`${
                animatedElements["intro-section"]
                  ? "animate__animated animate__fadeInRight"
                  : ""
              }`}
            >
              <div
                className="card-makonis-glass position-relative overflow-hidden"
                style={{ height: "400px", borderRadius: "15px" }}
              >
                <img
                  src={webDevPageData.intro.image}
                  alt="Web Development Team"
                  className="w-100 h-100"
                  style={{
                    objectFit: "cover",
                    transition: "transform 0.6s ease",
                    borderRadius: "15px",
                  }}
                  onMouseEnter={(e) => {
                    e.target.style.transform = "scale(1.05)";
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.transform = "scale(1)";
                  }}
                />
                <div
                  className="position-absolute inset-0"
                  style={{
                    background: `linear-gradient(135deg, rgba(${primaryRgb}, 0.1) 0%, transparent 50%, rgba(${secondaryRgb}, 0.1) 100%)`,
                    zIndex: 1,
                    borderRadius: "15px",
                  }}
                />
              </div>
            </Col>
          </Row>
        </Container>
      </section>

      {/* Services Section */}
      <section
        className="section-padding"
        id="services-section"
        ref={(el) => (elementsRef.current["services-section"] = el)}
        style={{ background: "rgba(255, 255, 255, 0.02)" }}
      >
        <Container className="container-makonis">
          <div className="text-center mb-5">
            <h2
              style={{
                fontSize: "clamp(1.5rem, 4vw, 2.25rem)",
                fontWeight: "800",
                letterSpacing: "2.6px",
                marginBottom: "1.5rem",
                background: "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
                WebkitBackgroundClip: "text",
                WebkitTextFillColor: "transparent",
                backgroundClip: "text",
                textShadow: "0 0 30px rgba(0, 160, 233, 0.3)",
              }}
            >
              Comprehensive Web Solutions
            </h2>
            <p
              className="lead mx-auto text-white"
              style={{ maxWidth: "1200px", opacity: 0.9, lineHeight: "1.5" }}
            >
              {/* No comment here, just pure style */}
              We offer a wide range of professional web and mobile development
              services, designed to meet the unique needs of your business. Our
              solutions are scalable, user-friendly, and tailored to help you
              grow and succeed in the digital world.
            </p>
          </div>

          {/* Service Tabs */}
          <div className="service-tabs mb-5">
            <div className="d-flex justify-content-center flex-wrap" style={{ gap: '1.5rem' }}>
              {webDevPageData.services.map((service) => (
                <button
                  key={service.id}
                  className={`btn rounded-pill px-5 py-3 fw-semibold transition-all duration-300 ${
                    activeService === service.id
                      ? "btn-makonis-primary"
                      : "btn-makonis-ghost"
                  }`}
                  onClick={() => setActiveService(service.id)}
                  style={{
                    border:
                      activeService === service.id
                        ? "none"
                        : `1px solid rgba(${secondaryRgb}, 0.3)`,
                    boxShadow:
                      activeService === service.id
                        ? `0 8px 25px rgba(${primaryRgb}, 0.3)`
                        : "none",
                    color: "white",
                  }}
                >
                  <i className={`fas ${service.icon} me-2`} style={{ color: "white" }}></i>
                  {service.title}
                </button>
              ))}
            </div>
          </div>

          {/* Active Service Content */}
          {webDevPageData.services.map((service) => (
            <div
              key={service.id}
              className={`service-content ${
                activeService === service.id ? "d-block" : "d-none"
              }`}
            >
              <Row className="align-items-center g-5">
                <Col lg={6} className="order-lg-2">
                  <div
                    className="card-makonis-glass position-relative overflow-hidden"
                    style={{ height: "400px", borderRadius: "15px" }}
                  >
                    <div
                      className="position-absolute inset-0"
                      style={{
                        background: `linear-gradient(135deg, ${service.color}40 0%, transparent 50%, rgba(${secondaryRgb}, 0.1) 100%)`,
                        zIndex: 1,
                        borderRadius: "15px",
                      }}
                    />
                    <img
                      src={service.image}
                      alt={service.title}
                      className="w-100 h-100"
                      style={{
                        objectFit: "cover",
                        transition: "transform 0.6s ease",
                        borderRadius: "15px",
                      }}
                      onMouseEnter={(e) => {
                        e.target.style.transform = "scale(1.05)";
                      }}
                      onMouseLeave={(e) => {
                        e.target.style.transform = "scale(1)";
                      }}
                    />
                  </div>
                </Col>
                <Col lg={6} className="order-lg-1">
                  <h3
                    className="display-5 fw-bold pb-4"
                    style={{
                      background: `linear-gradient(135deg, ${service.color} 0%, ${secondaryColor} 100%)`,
                      WebkitBackgroundClip: "text",
                      WebkitTextFillColor: "transparent",
                      backgroundClip: "text",
                    }}
                  >
                    {service.title}
                  </h3>
                  <p className="lead mb-4 text-white" style={{ opacity: 0.9, lineHeight: "1.5" }}>
                    {/* No comment here, just pure style */}
                    {service.description}
                  </p>
                  <div className="features-list">
                    {service.features.map((feature, index) => (
                      <div
                        key={index}
                        className="feature-item d-flex align-items-start mb-3"
                        style={{ transition: "all 0.3s ease", paddingBottom: "0.2rem" }}
                      >
                        <div
                          className="feature-icon me-3 d-flex align-items-center justify-content-center"
                          style={{
                            width: "40px",
                            height: "40px",
                            borderRadius: "50%",
                            background: `rgba(${secondaryRgb}, 0.2)`,
                            color: secondaryColor,
                            flexShrink: 0,
                            border: `1px solid rgba(${secondaryRgb}, 0.3)`,
                          }}
                        >
                          <i className="fas fa-check"></i>
                        </div>
                        <div>
                          <p
                            className="mb-0 text-white"
                            style={{ opacity: 0.9, lineHeight: "1.5" }}
                          >
                            {/* No comment here, just pure style */}
                            {feature}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </Col>
              </Row>
            </div>
          ))}
        </Container>
      </section>

      {/* Technologies Section */}
      <section
        className="section-padding"
        id="tech-section"
        ref={(el) => (elementsRef.current["tech-section"] = el)}
      >
        <Container className="container-makonis">
          <div className="text-center mb-5">
            <h2
              style={{
                fontSize: "3.6rem",
                fontWeight: "800",
                letterSpacing: "2.6px",
                paddingBottom: "1.2rem",
                background: "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
                WebkitBackgroundClip: "text",
                WebkitTextFillColor: "transparent",
                backgroundClip: "text",
                textShadow: "0 0 30px rgba(0, 160, 233, 0.3)",
              }}
            >
              {webDevPageData.technologies.title}
            </h2>
            <p
              className="lead mx-auto text-white"
              style={{ maxWidth: "1000px", opacity: 0.9, lineHeight: "1.5" }}
            >
              {/* No comment here, just pure style */}
              {webDevPageData.technologies.description}
            </p>
          </div>

          <Row className="justify-content-center">
            <Col lg={10}>
              <div className="tech-grid">
                <Row className="g-4">
                  {webDevPageData.technologies.techs.map((tech, index) => (
                    <Col xs={6} sm={4} md={3} key={index}>
                      <div
                        className={`card-makonis tech-item text-center p-4 h-100 ${
                          animatedElements["tech-section"]
                            ? "animate__animated animate__fadeIn"
                            : ""
                        }`}
                        style={{
                          animationDelay: `${index * 0.1}s`,
                          background: "rgba(255, 255, 255, 0.05)",
                          backdropFilter: "blur(5px)",
                          border: `1px solid rgba(${secondaryRgb}, 0.2)`,
                          transition: "all 0.3s ease",
                          transform: "translateY(0)",
                          borderRadius: "10px",
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.transform =
                            "translateY(-10px) scale(1.02)";
                          e.currentTarget.style.boxShadow = `0 20px 40px rgba(${secondaryRgb}, 0.2)`;
                          e.currentTarget.style.borderColor = secondaryColor;
                          e.currentTarget.style.background =
                            "rgba(255, 255, 255, 0.08)";
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.transform =
                            "translateY(0) scale(1)";
                          e.currentTarget.style.boxShadow =
                            "0 10px 25px rgba(0, 41, 86, 0.15)";
                          e.currentTarget.style.borderColor = `rgba(${secondaryRgb}, 0.2)`;
                          e.currentTarget.style.background =
                            "rgba(255, 255, 255, 0.05)";
                        }}
                      >
                        <div className="tech-icon mb-3">
                          <i
                            className={`${tech.icon} fa-3x`}
                            style={{ color: secondaryColor }}
                          ></i>
                        </div>
                        <h5 className="tech-name text-white fw-semibold">
                          {tech.name}
                        </h5>
                      </div>
                    </Col>
                  ))}
                </Row>
              </div>
            </Col>
          </Row>
        </Container>
      </section>

      {/* Global CSS for animations */}
      <style>{`
        @keyframes float {
          0%, 100% {
            transform: translateY(0) rotate(0deg);
          }
          50% {
            transform: translateY(-20px) rotate(10deg);
          }
        }

        @keyframes pulse {
          0% {
            transform: scale(0.8);
            opacity: 0.5;
          }
          50% {
            transform: scale(1.1);
            opacity: 0.8;
          }
          100% {
            transform: scale(0.8);
            opacity: 0.5;
          }
        }

        .section-padding {
          padding-top: 4rem;
          padding-bottom: 4rem;
        }

        .container-makonis {
          max-width: 1320px;
          margin-left: auto;
          margin-right: auto;
          padding-left: 15px;
          padding-right: 15px;
        }

        .transition-all {
          transition: all 0.3s ease;
        }

        .badge {
          font-weight: 600;
          letter-spacing: 0.5px;
        }

        .card-makonis-glass {
          background: rgba(255, 255, 255, 0.05);
          backdrop-filter: blur(10px);
          border: 1px solid rgba(${secondaryRgb}, 0.2);
          border-radius: 15px;
          box-shadow: 0 10px 25px rgba(0, 41, 86, 0.15);
          transition: all 0.3s ease;
        }

        .card-makonis-glass:hover {
          transform: translateY(-5px);
          box-shadow: 0 25px 50px rgba(0, 160, 233, 0.2);
        }

        .btn-makonis-primary {
          background: linear-gradient(135deg, ${primaryColor} 0%, ${secondaryColor} 100%);
          color: white !important;
          border: none;
        }
        .btn-makonis-primary:hover {
          transform: translateY(-2px) scale(1.02);
          background: linear-gradient(135deg, ${secondaryColor} 0%, ${primaryColor} 100%);
          box-shadow: 0 10px 20px rgba(${secondaryRgb}, 0.3);
        }

        .btn-makonis-ghost {
          background: transparent;
          color: white !important;
          border: 1px solid rgba(${secondaryRgb}, 0.3);
        }
        .btn-makonis-ghost:hover {
          background: rgba(255, 255, 255, 0.15);
          border-color: rgba(0, 160, 233, 0.5);
          color: white !important;
          transform: translateY(-2px);
        }

        /* Responsive adjustments for headings */
        @media (max-width: 768px) {
          .webdev-hero-section h1 {
            font-size: 2.5rem !important;
          }
          .webdev-hero-section p {
            font-size: 1rem !important;
          }
          h2 {
            font-size: 2.5rem !important;
          }
          h3 {
            font-size: 2rem !important;
          }
          .section-padding {
            padding-top: 3rem;
            padding-bottom: 3rem;
          }
          .btn.rounded-pill {
            padding-left: 1.5rem !important;
            padding-right: 1.5rem !important;
            font-size: 0.9rem;
          }
        }
      `}</style>
    </div>
  );
};

export default WebDevPage;