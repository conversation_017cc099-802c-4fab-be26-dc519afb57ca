import React, { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { debounce } from '../contexts/searchUtils';
import { popularSearches } from '../contexts/searchData';

const SearchBar = ({ 
  isOpen, 
  onToggle, 
  onClose, 
  className = '',
  placeholder = 'Search services, pages, technologies...',
  showIcon = true,
  autoFocus = false
}) => {
  const [query, setQuery] = useState('');
  const [suggestions, setSuggestions] = useState([]);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  
  const inputRef = useRef(null);
  const dropdownRef = useRef(null);
  const navigate = useNavigate();

  // Debounced search function
  const debouncedSearch = useRef(
    debounce(async (searchQuery) => {
      if (!searchQuery || searchQuery.trim().length < 2) {
        setSuggestions([]);
        setIsLoading(false);
        return;
      }

      try {
        // Import search utilities dynamically to avoid circular dependencies
        const { getSearchSuggestions } = await import('../contexts/searchUtils');
        const results = getSearchSuggestions(searchQuery.trim(), 6);
        setSuggestions(results);
      } catch (error) {
        console.error('Search error:', error);
        setSuggestions([]);
      } finally {
        setIsLoading(false);
      }
    }, 300)
  ).current;

  // Handle input change
  const handleInputChange = (e) => {
    const value = e.target.value;
    setQuery(value);
    setSelectedIndex(-1);
    setIsLoading(true);
    
    if (value.trim().length > 0) {
      setShowSuggestions(true);
      debouncedSearch(value);
    } else {
      setShowSuggestions(false);
      setSuggestions([]);
      setIsLoading(false);
    }
  };

  // Handle keyboard navigation
  const handleKeyDown = (e) => {
    if (!showSuggestions) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev < suggestions.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev > 0 ? prev - 1 : suggestions.length - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && suggestions[selectedIndex]) {
          handleSuggestionClick(suggestions[selectedIndex]);
        } else if (query.trim()) {
          handleSearch();
        }
        break;
      case 'Escape':
        e.preventDefault();
        handleClose();
        break;
    }
  };

  // Handle suggestion click
  const handleSuggestionClick = (suggestion) => {
    navigate(suggestion.path);
    handleClose();
  };

  // Handle search submission - just close, don't redirect
  const handleSearch = () => {
    // Don't redirect to search page, users should select from suggestions
    handleClose();
  };

  // Handle popular search click - trigger search to show results
  const handlePopularSearchClick = (searchTerm) => {
    setQuery(searchTerm);
    setIsLoading(true);
    setShowSuggestions(true);
    debouncedSearch(searchTerm);
  };

  // Handle close
  const handleClose = () => {
    setQuery('');
    setSuggestions([]);
    setShowSuggestions(false);
    setSelectedIndex(-1);
    setIsLoading(false);
    if (onClose) onClose();
  };

  // Handle input focus
  const handleFocus = () => {
    if (query.trim().length === 0) {
      setShowSuggestions(true);
    }
  };

  // Handle click outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        dropdownRef.current && 
        !dropdownRef.current.contains(event.target) &&
        inputRef.current &&
        !inputRef.current.contains(event.target)
      ) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Auto focus when opened
  useEffect(() => {
    if (isOpen && autoFocus && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen, autoFocus]);

  if (!isOpen) {
    return showIcon ? (
      <button
        className="btn"
        onClick={onToggle}
        aria-label="Open search"
        style={{
          minWidth: '44px',
          minHeight: '44px',
          border: '2px solid #002956',
          background: '#00a0e9',
          borderRadius: '8px',
          color: 'white',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          transition: 'all 0.3s ease'
        }}
      >
        <i className="fas fa-search" style={{ fontSize: '16px' }}></i>
      </button>
    ) : null;
  }

  return (
    <div className={`search-inline-desktop position-relative ${className}`} style={{ minWidth: '300px', maxWidth: '500px' }}>
      <div className="position-relative">
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={handleFocus}
          placeholder={placeholder}
          className="search-input-enhanced"
          style={{
            paddingRight: '50px'
          }}
          autoComplete="off"
          aria-label="Search"
          aria-expanded={showSuggestions}
          aria-haspopup="listbox"
        />
        
        <div className="position-absolute top-50 end-0 translate-middle-y d-flex align-items-center pe-3">
          {isLoading ? (
            <div className="spinner-border spinner-border-sm text-makonis-secondary" role="status">
              <span className="visually-hidden">Searching...</span>
            </div>
          ) : (
            <button
              type="button"
              onClick={query.trim() ? handleSearch : handleClose}
              className="btn btn-link p-0"
              style={{ color: '#002956' }}
              aria-label={query.trim() ? 'Search' : 'Close search'}
            >
              <i className={`fas ${query.trim() ? 'fa-search' : 'fa-times'}`} style={{ fontSize: '14px' }}></i>
            </button>
          )}
        </div>
      </div>

      {showSuggestions && (
        <div ref={dropdownRef} className="search-dropdown-enhanced">
          {suggestions.length > 0 ? (
            <>
              <div className="px-3 py-2 border-bottom">
                <small className="text-muted fw-semibold">Suggestions</small>
              </div>
              {suggestions.map((suggestion, index) => (
                <div
                  key={suggestion.id}
                  className={`search-item-enhanced ${index === selectedIndex ? 'active' : ''}`}
                  onClick={() => handleSuggestionClick(suggestion)}
                  role="option"
                  aria-selected={index === selectedIndex}
                >
                  <div className="d-flex align-items-center">
                    <i className={`${suggestion.icon} text-makonis-secondary me-3`} style={{ width: '16px' }}></i>
                    <div className="flex-grow-1">
                      <div className="search-item-title-enhanced">{suggestion.title}</div>
                      <div className="search-item-type-enhanced text-capitalize">{suggestion.type}</div>
                    </div>
                  </div>
                </div>
              ))}
            </>
          ) : query.trim().length === 0 ? (
            <>
              <div className="px-3 py-2 border-bottom">
                <small className="text-muted fw-semibold">Popular Searches</small>
              </div>
              {popularSearches.slice(0, 5).map((searchTerm, index) => (
                <div
                  key={index}
                  className="search-item-enhanced"
                  onClick={() => handlePopularSearchClick(searchTerm)}
                >
                  <div className="d-flex align-items-center">
                    <i className="fas fa-clock text-gray-400 me-3" style={{ width: '16px' }}></i>
                    <div className="search-item-title-enhanced">{searchTerm}</div>
                  </div>
                </div>
              ))}
            </>
          ) : (
            <div className="search-dropdown-empty-enhanced">
              <i className="fas fa-search"></i>
              <div>No suggestions found</div>
              <small className="text-muted">Try a different search term</small>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default SearchBar;
