import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

/**
 * Custom hook to scroll to top on route change
 * Ensures consistent scroll behavior across all pages
 */
const useScrollToTop = () => {
  const location = useLocation();

  useEffect(() => {
    // Scroll to top immediately
    window.scrollTo({
      top: 0,
      left: 0,
      behavior: 'instant' // Use instant to prevent conflicts with animations
    });

    // Also ensure document scroll is reset
    document.documentElement.scrollTop = 0;
    document.body.scrollTop = 0;

    // Prevent scroll restoration
    if ('scrollRestoration' in history) {
      history.scrollRestoration = 'manual';
    }
  }, [location.pathname]);
};

export default useScrollToTop;
