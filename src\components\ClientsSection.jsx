import React, { useState, useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

// Enhanced SVG Blob for a more modern feel
const ModernBlob1 = () => (
  <svg width="500" height="500" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg" style={{ zIndex: 0, opacity: 0.08, position: 'absolute', top: '-100px', right: '-150px' }}>
    <defs>
      <linearGradient id="clientsBlobGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style={{ stopColor: '#007bff', stopOpacity: 1 }} />
        <stop offset="100%" style={{ stopColor: '#00a0e9', stopOpacity: 1 }} />
      </linearGradient>
    </defs>
    <path fill="url(#clientsBlobGradient1)" d="M60.1,-66.9C76.5,-56.8,87.5,-37.9,89.9,-18.3C92.3,1.3,86.1,21.5,74.1,37.9C62.1,54.3,44.3,67,25.5,73.7C6.7,80.4,-13.2,81.2,-30.9,74.8C-48.6,68.4,-64.1,54.8,-72.4,38.2C-80.7,21.6,-81.8,2,-76.5,-16.1C-71.2,-34.2,-59.5,-50.8,-44.4,-61C-29.3,-71.1,-10.8,-74.7,9.3,-77.2C29.4,-79.7,51.1,-82.3,60.1,-66.9Z" transform="translate(100 100) scale(1.1)" />
  </svg>
);

const ModernBlob2 = () => (
  <svg width="400" height="400" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg" style={{ zIndex: 0, opacity: 0.07, position: 'absolute', bottom: '-120px', left: '-100px' }}>
     <defs>
      <linearGradient id="clientsBlobGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style={{ stopColor: '#6f42c1', stopOpacity: 1 }} />
        <stop offset="100%" style={{ stopColor: '#00a0e9', stopOpacity: 1 }} />
      </linearGradient>
    </defs>
    <path fill="url(#clientsBlobGradient2)" d="M50.9,-57.9C65.9,-47.7,78.1,-33.5,81.2,-17.2C84.3,-0.9,78.3,17.6,67.3,32.8C56.3,48,40.3,59.9,23.3,66.6C6.2,73.3,-11.9,74.8,-28.7,69.8C-45.5,64.8,-61,53.3,-69.5,38.3C-77.9,23.3,-79.3,4.8,-74.7,-12.6C-70.1,-30,-59.5,-46.3,-45.7,-56.6C-31.9,-66.9,-14.9,-71.2,2.4,-73.2C19.7,-75.2,35.9,-68.1,50.9,-57.9Z" transform="translate(100 100) scale(0.9)" />
  </svg>
);

import client1 from '../assets/Picture1.png';
import client2 from '../assets/Picture2.png';
import client3 from '../assets/Picture3.png';
import client4 from '../assets/Picture4.png';
import client5 from '../assets/Picture5.png';
import client6 from '../assets/Picture6.png';
import client7 from '../assets/Picture7.png';
import client8 from '../assets/Picture8.png';
import client10 from '../assets/Picture10.png';
import client11 from '../assets/Picture11.png';
import client12 from '../assets/Picture12.png';
import client13 from '../assets/Picture13.png';
import client14 from '../assets/Picture14.png';
import client15 from '../assets/Picture15.png';
import client16 from '../assets/Picture16.png';
import client17 from '../assets/Picture17.png';
import client18 from '../assets/Picture18.png';
import client19 from '../assets/Picture19.png';
import client20 from '../assets/Picture20.png';
import client21 from '../assets/Picture21.png';
import client22 from '../assets/Picture22.png';
import client23 from '../assets/Picture23.png';
import client24 from '../assets/Picture24.png';
import client25 from '../assets/Picture25.png';
import client26 from '../assets/Picture26.png';
import client27 from '../assets/Picture27.png';
import client28 from '../assets/Picture28.png';
import client29 from '../assets/Picture29.png';
import client30 from '../assets/Picture30.png';
import client31 from '../assets/Picture31.png';
import client32 from '../assets/Picture32.png';
import client33 from '../assets/Picture33.png';
import client34 from '../assets/Picture34.png';
import client35 from '../assets/Picture35.svg';
import client36 from '../assets/Picture36.png';
import client37 from '../assets/Picture37.png';
import client38 from '../assets/Picture38.svg';

const ClientsSection = () => {
  const clients = [
    { id: 'client1', name: 'Client 1', src: client1, alt: 'Client 1 Logo' },
    { id: 'client2', name: 'Client 2', src: client2, alt: 'Client 2 Logo' },
    { id: 'client3', name: 'Client 3', src: client3, alt: 'Client 3 Logo' },
    { id: 'client4', name: 'Client 4', src: client4, alt: 'Client 4 Logo' },
    { id: 'client5', name: 'Client 5', src: client5, alt: 'Client 5 Logo' },
    { id: 'client6', name: 'Client 6', src: client6, alt: 'Client 6 Logo' },
    { id: 'client7', name: 'Client 7', src: client7, alt: 'Client 7 Logo' },
    { id: 'client8', name: 'Client 8', src: client8, alt: 'Client 8 Logo' },
    { id: 'client10', name: 'Client 10', src: client10, alt: 'Client 10 Logo' },
    { id: 'client11', name: 'Client 11', src: client11, alt: 'Client 11 Logo' },
    { id: 'client12', name: 'Client 12', src: client12, alt: 'Client 12 Logo' },
    { id: 'client13', name: 'Client 13', src: client13, alt: 'Client 13 Logo' },
    { id: 'client14', name: 'Client 14', src: client14, alt: 'Client 14 Logo' },
    { id: 'client15', name: 'Client 15', src: client15, alt: 'Client 15 Logo' },
    { id: 'client16', name: 'Client 16', src: client16, alt: 'Client 16 Logo' },
    { id: 'client17', name: 'Client 17', src: client17, alt: 'Client 17 Logo' },
    { id: 'client18', name: 'Client 18', src: client18, alt: 'Client 18 Logo' },
    { id: 'client19', name: 'Client 19', src: client19, alt: 'Client 19 Logo' },
    { id: 'client20', name: 'Client 20', src: client20, alt: 'Client 20 Logo' },
    { id: 'client21', name: 'Client 21', src: client21, alt: 'Client 21 Logo' },
    { id: 'client22', name: 'Client 22', src: client22, alt: 'Client 22 Logo' },
    { id: 'client23', name: 'Client 23', src: client23, alt: 'Client 23 Logo' },
    { id: 'client24', name: 'Client 24', src: client24, alt: 'Client 24 Logo' },
    { id: 'client25', name: 'Client 25', src: client25, alt: 'Client 25 Logo' },
    { id: 'client26', name: 'Client 26', src: client26, alt: 'Client 26 Logo' },
    { id: 'client27', name: 'Client 27', src: client27, alt: 'Client 27 Logo' },
    { id: 'client28', name: 'Client 28', src: client28, alt: 'Client 28 Logo' },
    { id: 'client29', name: 'Client 29', src: client29, alt: 'Client 29 Logo' },
    { id: 'client30', name: 'Client 30', src: client30, alt: 'Client 30 Logo' },
    { id: 'client31', name: 'Client 31', src: client31, alt: 'Client 31 Logo' },
    { id: 'client32', name: 'Client 32', src: client32, alt: 'Client 32 Logo' },
    { id: 'client33', name: 'Client 33', src: client33, alt: 'Client 33 Logo' },
    { id: 'client34', name: 'Client 34', src: client34, alt: 'Client 34 Logo' },
    { id: 'client35', name: 'Client 35', src: client35, alt: 'Client 35 Logo' },
    { id: 'client36', name: 'Client 36', src: client36, alt: 'Client 36 Logo' },
    { id: 'client37', name: 'Client 37', src: client37, alt: 'Client 37 Logo' },
    { id: 'client38', name: 'Client 38', src: client38, alt: 'Client 38 Logo' }
  ];

  const [hoveredClient, setHoveredClient] = useState(null);
  const [isScrollingPaused, setIsScrollingPaused] = useState(false);
  const scrollerRef = useRef(null);
  const [animationTotalWidth, setAnimationTotalWidth] = useState(0);

  // GSAP animation refs
  const sectionRef = useRef(null);
  const titleRef = useRef(null);
  const clientsContainerRef = useRef(null);
  const backgroundRef = useRef(null);

  // Define these as constants for easier updates
  const LOGO_WIDTH_PX = 170; // Width of each logo container
  const LOGO_MARGIN_X_REM = 1; // mx-4 is 1rem on each side
  const LOGO_MARGIN_X_PX = LOGO_MARGIN_X_REM * 5; // Assuming 1rem = 16px for calc

  useEffect(() => {
    // Calculate the total width for the translateX animation
    // This calculation is for one full set of original logos plus their margins
    const totalWidth = (LOGO_WIDTH_PX * clients.length) + (LOGO_MARGIN_X_PX * 2 * clients.length);
    setAnimationTotalWidth(totalWidth);
  }, [clients.length]);

  // GSAP scroll-triggered animations (from ServicesSection)
  useEffect(() => {
    const ctx = gsap.context(() => {
      // Title animation
      gsap.from(titleRef.current, {
        y: 50,
        opacity: 0,
        duration: 1,
        ease: "power2.out",
        scrollTrigger: {
          trigger: titleRef.current,
          start: "top 80%",
          end: "bottom 20%",
          toggleActions: "play none none reverse"
        }
      });

      // Clients container stagger animation
      gsap.from(clientsContainerRef.current, {
        y: 80,
        opacity: 0,
        duration: 0.8,
        ease: "power2.out",
        scrollTrigger: {
          trigger: sectionRef.current,
          start: "top 70%",
          end: "bottom 20%",
          toggleActions: "play none none reverse"
        }
      });

      // Background parallax effect
      gsap.to(backgroundRef.current, {
        yPercent: -50,
        ease: "none",
        scrollTrigger: {
          trigger: sectionRef.current,
          start: "top bottom",
          end: "bottom top",
          scrub: true
        }
      });

    }, sectionRef);

    return () => ctx.revert();
  }, []);


  const clientItemStyle = (isHovered) => ({
    width: `${LOGO_WIDTH_PX}px`,
    height: '90px', // Reduced height for better fit
    transition: 'transform 0.3s ease, box-shadow 0.3s ease, opacity 0.3s ease',
    backgroundColor: '#fff', // White background for logos to stand out against gray
    // opacity: isHovered ? 1 : 0.9, // Less dimming for better visibility
    // transform: isHovered ? 'translateY(-6px) scale(1.05)' : 'translateY(0) scale(1)',
    // boxShadow: isHovered ? '0 10px 25px rgba(0, 0, 0, 0.12)' : '0 4px 10px rgba(0, 0, 0, 0.06)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: '0.75rem', // Bootstrap rounded-3
    border: '1px solid #e9ecef', // Softer border
    padding: '7px',
    cursor: 'pointer'
  });

  const renderClientLogos = (keyPrefix) => clients.map((client) => (
    <div
      key={`${keyPrefix}-${client.id}`}
      className="mx-3" // Bootstrap margin utility - increased from mx-3
      style={clientItemStyle(hoveredClient === client.id)}
      onMouseEnter={() => setHoveredClient(client.id)}
      onMouseLeave={() => setHoveredClient(null)}
      title={client.name} // Good for accessibility
    >
      <img
        src={client.src}
        alt={client.alt}
        className="p-2 max-w-full max-h-full object-contain transition-all duration-300"
        style={{
          filter: hoveredClient === client.id || hoveredClient === null ? 'none' : 'grayscale(30%)'
        }}
      />
    </div>
  ));

  return (
    <section
      ref={sectionRef}
      className="section-padding relative overflow-hidden"
      id="clients-section"
      style={{
        background: 'linear-gradient(135deg, rgba(0, 41, 86, 0.95) 0%, rgba(0, 41, 86, 0.98) 100%)',
        backdropFilter: 'blur(10px)'
      }}
    >
      {/* Enhanced Background Elements */}
      <div ref={backgroundRef} className="absolute inset-0 z-0">
        {/* Animated Grid Pattern */}
        <div
          className="absolute inset-0 opacity-20"
          style={{
            backgroundImage: `
              linear-gradient(rgba(0, 160, 233, 0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(0, 160, 233, 0.1) 1px, transparent 1px)
            `,
            backgroundSize: '50px 50px',
          }}
        />

        {/* Floating Tech Icons */}
        {[
          // { lordicon: 'https://cdn.lordicon.com/qhviklyi.json', top: '10%', left: '5%', delay: 0 },
          // { lordicon: 'https://cdn.lordicon.com/kiynvdns.json', top: '20%', right: '8%', delay: 1 },
          // { lordicon: 'https://cdn.lordicon.com/hwjcdycb.json', bottom: '15%', left: '3%', delay: 2 },
          // { lordicon: 'https://cdn.lordicon.com/qhgmphtg.json', bottom: '25%', right: '5%', delay: 3 }
        ].map((item, index) => (
          <div
            key={index}
            className="absolute w-15 h-15 bg-makonis-secondary/10 rounded-full flex items-center justify-center backdrop-blur-sm border border-makonis-secondary/20 animate-float"
            style={{
              ...item,
              animationDelay: `${item.delay}s`
            }}
          >
            <lord-icon
              src={item.lordicon}
              trigger="hover"
              colors="primary:#00a0e9"
              style={{ width: '24px', height: '24px' }}>
            </lord-icon>
          </div>
        ))}

        {/* <ModernBlob1 />
        <ModernBlob2 /> */}
      </div>
      <div className="container-makonis relative z-10">
        <div ref={titleRef} className="text-center mb-16 lg:mb-24">
          {/* Enhanced Badge */}
        
        

          <h2  style={{
            fontSize: "3.6rem",
            fontWeight: "800",
            letterSpacing: "2.6px",
            marginBottom: "1rem",
            background: 'linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text',
            textShadow: '0 0 30px rgba(0, 160, 233, 0.3)'
          }}>
            Trusted By Industry Leaders
          </h2>

          <p className="text-xl text-white/90 leading-relaxed mx-auto mb-4 max-w-3xl">
            We're proud to collaborate with a diverse portfolio of clients across industries,
            helping them achieve exceptional results through innovative solutions.
          </p>

          {/* Enhanced Accent Line */}
          <div className="w-30 h-1 mx-auto relative">
            <div
              className="w-full h-full rounded-sm shadow-glow"
              style={{
                background: 'linear-gradient(90deg, transparent, #00a0e9, transparent)',
              }}
            />
          </div>
        </div>

        <div
          ref={clientsContainerRef}
          className="rounded-3xl shadow-2xl p-6 relative overflow-hidden"
          style={{
            maskImage: 'linear-gradient(to right, transparent 0%, black 5%, black 95%, transparent 100%)',
            WebkitMaskImage: 'linear-gradient(to right, transparent 0%, black 5%, black 95%, transparent 100%)',
            background: 'rgba(169, 169, 169, 0.3)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255, 255, 255, 0.2)'
          }}
          onMouseEnter={() => setIsScrollingPaused(true)}
          onMouseLeave={() => setIsScrollingPaused(false)}
        >
          <div
            ref={scrollerRef}
            className="flex"
            style={{
              animation: animationTotalWidth > 0 ? `scrollClients 60s linear infinite` : 'none',
              animationPlayState: isScrollingPaused ? 'paused' : 'running',
              width: 'max-content'
            }}
          >
            {renderClientLogos('original')}
            {renderClientLogos('duplicate')}
          </div>
        </div>
      </div>

      {/* Add keyframes animation using style jsx */}
      <style>{`
        @keyframes scrollClients {
          0% {
            transform: translateX(0);
          }
          100% {
            /* The translateX amount should be the total width of ONE set of original logos including their horizontal margins.
              - Each logo container width: ${LOGO_WIDTH_PX}px
              - Each logo container has mx-4 (1rem margin on left, 1rem on right = 2rem total horizontal margin per logo)
              - Assuming 1rem = 16px, so 2rem = 32px.
              - Total width for one logo item + its margins = ${LOGO_WIDTH_PX}px + (2 * ${LOGO_MARGIN_X_PX})px
              - Number of clients: ${clients.length}
              - Dynamic calculation: calc(-1 * ${animationTotalWidth}px)
            */
            transform: translateX(-${animationTotalWidth}px);
          }
        }

        /* Optional: Pause animation for users who prefer reduced motion */
        @media (prefers-reduced-motion: reduce) {
          .d-flex[style*="animationName:scrollClients"],
          .d-flex[style*="animationName: scrollClients"] { /* For browsers that add space */
            animation-play-state: paused !important;
          }
        }
      `}</style>
    </section>
  );
};

export default ClientsSection;