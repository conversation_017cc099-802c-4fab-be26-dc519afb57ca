import React, { useEffect, useRef } from "react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { useNavigate } from "react-router-dom"; // Add this import
import ServicesSection from "../components/ServicesSection";
import ClientsSection from "../components/ClientsSection";
import QuickTestingSection from "../components/QuickTestingSection";
import homePagevideo2 from "../assets/homepagevideo2.mp4";

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger);

const HomePage = () => {
  // Refs for animation targets
  const heroRef = useRef(null);
  const mainTitleRef = useRef(null);
  const subtitleRef = useRef(null);

  // Navigation hook
  const navigate = useNavigate();

  // Hero section animations (buttons animation removed)
  useEffect(() => {
    const ctx = gsap.context(() => {
      const tl = gsap.timeline({ delay: 0.2 });

      // Main title slides in from top
      if (mainTitleRef.current) {
        tl.from(mainTitleRef.current, {
          y: -100,
          opacity: 0,
          duration: 1.2,
          ease: "power3.out",
        });
      }
    }, heroRef);

    return () => ctx.revert();
  }, []);

  // Navigation handlers
  const handleButtonClick = (service) => {
    switch (service) {
      case "it-staffing":
        navigate("/services/staff-augmentation-new");
        break;
      case "international-staffing":
        navigate("/services/staff-augmentation-new");
        break;
      case "talent-services":
        navigate("/professional-talent-services");
        break;
      case "tech-services":
        navigate("/products");
        break;
      default:
        console.log("Unknown service");
    }
  };

  return (
    <>
      <style>{`
        /* --- Hero Button Styles --- */
        .hero-button {
            background-color: rgba(248, 249, 250, 0.95); /* Light, almost-opaque background */
            color: #002956; /* Dark blue text for better readability */
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 12px 28px;
            border-radius: 50px; /* Pill shape */
            font-size: 1rem;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease-in-out;
            cursor: pointer;
            margin: 5px;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            opacity: 1;
            visibility: visible;
        }

        .hero-button:hover {
            background: linear-gradient(135deg, #002956 100%); /* Makonis gradient on hover */
            color: #ffffff;
            border-color: #00a0e9;
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0, 160, 233, 0.3);
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .hero-button {
                font-size: 0.9rem;
                padding: 10px 20px;
                margin: 3px;
            }
        }
      `}</style>

      <section
        ref={heroRef}
        className="position-relative overflow-hidden"
        style={{
          minHeight: "90vh",
          display: "flex",
          alignItems: "center",
        }}
      >
        {/* Video Background */}
        <div className="position-absolute w-100 h-100" style={{ zIndex: -1 }}>
          <video
            autoPlay
            muted
            loop
            playsInline
            className="w-100 h-100"
            style={{
              objectFit: "cover",
              objectPosition: "center",
            }}
          >
            <source src={homePagevideo2} type="video/mp4" />
            Your browser does not support the video tag.
          </video>
        </div>

        {/* Video Overlay for Text Readability */}
        <div
          className="position-absolute w-100 h-100"
          style={{
            zIndex: 1,
            backgroundColor: "rgba(0, 0, 0, 0.5)",
          }}
        />

        {/* Content */}
        <div className="container position-relative" style={{ zIndex: 1 }}>
          <div className="row align-items-center justify-content-center text-center">
            <div className="col-lg-10 col-xl-8">
              {/* Main Heading */}
              <h1
                ref={mainTitleRef}
                className="display-2 fw-bold mb-3"
                style={{
                  fontSize: "clamp(2.5rem, 8vw, 4rem)",
                  lineHeight: "1.2",
                  color: "#ffffff",
                }}
              >
                Powering Your Growth with <span style={{ color: '#00a0e9' }}> Technology and Talent </span> 
              </h1>
              {/* <p
                ref={subtitleRef}
                className="lead mb-5"
                style={{
                  fontSize: "clamp(1rem, 2.5vw, 1.3rem)",
                  lineHeight: "1.6",
                  color: "rgba(255, 255, 255, 0.9)",
                  maxWidth: "1000px",
                  margin: "0 auto", // Center the paragraph
                  textShadow: "0 0 10px rgba(0, 0, 0, 0.8)",
                }}
              >
                We provide tailored IT staffing and managed service solutions
                designed to accelerate your business. Whether you're scaling
                your technology teams or seeking full-spectrum IT support, our
                expert-driven approach ensures you get the right talent and
                services—fast, flexible, and aligned to your goals
              </p> */}

              {/* Buttons Section - Animation Removed */}
              <div
                className="d-flex flex-wrap justify-content-center gap-3 mt-5"
                style={{ minHeight: "60px" }}
              >
                <button
                  className="hero-button"
                  onClick={() => handleButtonClick("it-staffing")}
                >
                  Staff Augmentation
                </button>
                <button
                  className="hero-button"
                  onClick={() => handleButtonClick("international-staffing")}
                >
                  International Staffing
                </button>
                <button
                  className="hero-button"
                  onClick={() => handleButtonClick("talent-services")}
                >
                  Managed Services
                </button>
                <button
                  className="hero-button"
                  onClick={() => handleButtonClick("tech-services")}
                >
                  Products
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      <ServicesSection />
      <ClientsSection />
      <QuickTestingSection />
    </>
  );
};

export default HomePage;
