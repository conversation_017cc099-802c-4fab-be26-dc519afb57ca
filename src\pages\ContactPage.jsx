import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from "react-leaflet";
import L from "leaflet";
import "leaflet/dist/leaflet.css";
import "@fortawesome/fontawesome-free/css/all.min.css";

// --- Import your local images ---
import banpic1 from "../assets/banpic1.webp";
import banpic2 from "../assets/banpic2.webp";
import banpic3 from "../assets/banpic3.webp";
import banpic4 from "../assets/banpic4.webp";

import hydpic1 from "../assets/hydpic1.webp";
import hydpic2 from "../assets/hydpic2.webp";
import hydpic3 from "../assets/hydpic3.webp";
import hydpic4 from "../assets/hydpic4.webp";

// --- Carousel Image Data ---
const globalOfficeImages = [
  [
    { id: 1, src: banpic1, alt: "Global Office View 1" },
    { id: 2, src: hydpic1, alt: "Global Office View 2" },
    { id: 3, src: banpic3, alt: "Global Office View 3" },
    { id: 4, src: hydpic4, alt: "Global Office View 4" },
  ],
  [
    { id: 5, src: banpic2, alt: "Team Collaboration" },
    { id: 6, src: hydpic2, alt: "Modern Workspace" },
    { id: 7, src: banpic4, alt: "Conference Room" },
    { id: 8, src: hydpic3, alt: "Innovation Hub" },
  ],
  [
    { id: 9, src: hydpic3, alt: "Development Center" },
    { id: 10, src: banpic1, alt: "Tech Environment" },
    { id: 11, src: hydpic4, alt: "Meeting Space" },
    { id: 12, src: banpic2, alt: "Open-plan Office" },
  ],
];

const OfficeImageCarousel = () => {
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [currentSetIndex, setCurrentSetIndex] = useState(0);
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSetIndex(
        (prevIndex) => (prevIndex + 1) % globalOfficeImages.length
      );
    }, 5000);
    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    setIsTransitioning(true);
    const timer = setTimeout(() => setIsTransitioning(false), 300);
    return () => clearTimeout(timer);
  }, [currentSetIndex]);

  const currentImages = globalOfficeImages[currentSetIndex];

  return (
    <div className="relative h-full">
      <div
        className={`transition-opacity duration-300 h-full ${
          isTransitioning ? "opacity-50" : "opacity-100"
        }`}
      >
        {isMobile ? (
          // --- MOBILE: only show one image ---
          <div className="relative overflow-hidden rounded-lg bg-gradient-to-br from-makonis-primary/20 to-makonis-secondary/20 aspect-video">
            <img
              src={currentImages[0].src}
              alt={currentImages[0].alt}
              className="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
              onError={(e) => {
                e.target.src =
                  "https://via.placeholder.com/400x300/002956/FFFFFF?text=Image+Not+Found";
              }}
            />
            <div className="absolute inset-0 bg-gradient-to-t from-makonis-primary/70 via-transparent to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300">
              <div className="absolute bottom-2 left-2 right-2">
                <p className="text-white text-xs font-medium">
                  {currentImages[0].alt}
                </p>
              </div>
            </div>
          </div>
        ) : (
          // --- DESKTOP: full grid ---
          <div className="grid grid-cols-2 gap-3 h-full">
            {currentImages.map((image) => (
              <div
                key={`global-${currentSetIndex}-${image.id}`}
                className="relative overflow-hidden rounded-lg bg-gradient-to-br from-makonis-primary/20 to-makonis-secondary/20 group aspect-video"
              >
                <img
                  src={image.src}
                  alt={image.alt}
                  className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
                  onError={(e) => {
                    e.target.src =
                      "https://via.placeholder.com/400x300/002956/FFFFFF?text=Image+Not+Found";
                  }}
                />
                <div className="absolute inset-0 bg-gradient-to-t from-makonis-primary/70 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="absolute bottom-2 left-2 right-2">
                    <p className="text-white text-xs font-medium">
                      {image.alt}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* --- MODIFIED NAVIGATION BUTTONS --- */}
      <button
        onClick={() =>
          setCurrentSetIndex((prev) =>
            prev === 0 ? globalOfficeImages.length - 1 : prev - 1
          )
        }
        className="absolute top-1/2 -translate-y-1/2 z-10 hover:bg-makonis-primary text-white rounded-full transition-all duration-300"
        aria-label="Previous set"
        style={{ left: "-1.7rem" }}
      >
        <svg
          className="w-7 h-7"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M15 19l-7-7 7-7"
          />
        </svg>
      </button>

      <button
        onClick={() =>
          setCurrentSetIndex((prev) => (prev + 1) % globalOfficeImages.length)
        }
        className="absolute top-1/2 -translate-y-1/2 right-3 hover:bg-makonis-primary text-white rounded-full transition-all duration-300"
        aria-label="Next set"
        style={{ right: "-1.7rem" }}
      >
        <svg
          className="w-7 h-7"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M9 5l7 7-7 7"
          />
        </svg>
      </button>
    </div>
  );
};

const createCustomMarkerIcon = (isActive = false) => {
  const primaryColor = "#00a0e9";
  const whiteColor = "#ffffff";
  const size = isActive ? 42 : 32;
  const innerCircleRadius = isActive ? 3.5 : 2.5;
  const shadowOpacity = isActive ? 0.6 : 0.4;

  const iconSvg = btoa(`
    <svg width="${size}" height="${size}" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
          <feDropShadow dx="0" dy="3" stdDeviation="3" flood-color="${primaryColor}" flood-opacity="${shadowOpacity}" />
        </filter>
      </defs>
      <path
        d="M20 38 C20 38 35 24 35 15 C35 6.7 28.3 0 20 0 C11.7 0 5 6.7 5 15 C5 24 20 38 20 38 Z"
        fill="${primaryColor}"
        filter="url(#shadow)"
      />
      <circle cx="20" cy="15" r="${innerCircleRadius}" fill="${whiteColor}" />
    </svg>
  `);

  return L.icon({
    iconUrl: `data:image/svg+xml;base64,${iconSvg}`,
    iconSize: [size, size],
    iconAnchor: [size / 2, size],
    popupAnchor: [0, -size],
  });
};

// --- Main Contact Page Component ---
const ContactPage = () => {
  const [activeTab, setActiveTab] = useState("bangalore");
  const [mapState, setMapState] = useState({
    center: [25, 45],
    zoom: 1.5,
  });
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);

  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth < 768;
      setIsMobile(mobile);

      if (mobile) {
        setMapState({ center: [20, 20], zoom: 1.2 });
      } else {
        setMapState({ center: [25, 45], zoom: 1.6 });
      }
    };

    window.addEventListener("resize", handleResize);
    handleResize();
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const locations = [
    {
      id: "bangalore",
      title: "Bangalore",
      fullTitle: "Bangalore Office",
      address:
        "51, 3rd Cross Rd, Aswath Nagar, Marathahalli, Bengaluru, Karnataka 560037",
      phone: "+91 8041707838",
      email: "<EMAIL>",
      coords: [12.958057, 77.702026],
    },
    {
      id: "hyderabad",
      title: "Hyderabad",
      fullTitle: "Hyderabad Office",
      address:
        "5th Floor, Modern Profound Tech Park, HITEC City, Kondapur, Telangana 500081",
      phone: "+91 8041707838",
      email: "<EMAIL>",
      coords: [17.4593, 78.3659],
    },
    {
      id: "vijayawada",
      title: "Vijayawada",
      fullTitle: "Vijayawada Office",
      address:
        "71-3-8A, Koneru vari St, Patamata, Benz Circle, Vijayawada, Andhra Pradesh 520010",
      phone: "+91 8041707838",
      email: "<EMAIL>",
      coords: [16.4952, 80.6496],
    },
    {
      id: "texas",
      title: "Texas",
      fullTitle: "Texas Office",
      address:
        "Suite -410 Office – T Kings Plaza 14111 King Rd Frisco TX 75036",
      phone: "****** 525 8121",
      email: "<EMAIL>",
      coords: [33.1497, -96.7972],
    },
    {
      id: "melbourne",
      title: "Melbourne",
      fullTitle: "Melbourne Office",
      address: "54. Mansfield ST Berwick VIC 3806",
      phone: "+61 3 9707 1122",
      email: "<EMAIL>",
      coords: [-38.0322, 145.3473],
    },
    {
      id: "ontario",
      title: "Ontario",
      fullTitle: "Ontario Office",
      address: "4503 Glen Erin Dr., Mississauga, ON, Canada L5M 4G5",
      phone: "****** 828 2222",
      email: "<EMAIL>",
      coords: [43.5674, -79.7072],
    },
  ];

  const currentOffice = locations.find((loc) => loc.id === activeTab);

  const defaultIcon = createCustomMarkerIcon(false);
  const activeIcon = createCustomMarkerIcon(true);

  return (
    <div
      className="min-h-screen relative overflow-x-auto"
      style={{
        background:
          "linear-gradient(135deg, rgba(0, 41, 86, 0.95) 0%, rgba(0, 41, 86, 0.98) 100%)",
      }}
    >
      <div className="absolute inset-0 z-0">
        <div
          className="absolute inset-0 opacity-20"
          style={{
            backgroundImage: `linear-gradient(rgba(0, 160, 233, 0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(0, 160, 233, 0.1) 1px, transparent 1px)`,
            backgroundSize: "50px 50px",
          }}
        />
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12 sm:py-16 lg:py-20 relative z-10">
        <div className="text-center mb-10">
          <h1
            className="text-3xl sm:text-4xl lg:text-5xl font-extrabold tracking-tight"
            style={{
              background: "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              textShadow: "0 0 30px rgba(0, 160, 233, 0.3)",
            }}
          >
            Our Global Presence
          </h1>

          <p className="mt-4 text-base sm:text-lg text-gray-300 max-w-3xl mx-auto leading-relaxed">
            Connect with us at any of our strategically located offices around
            the world. We're here to serve you with excellence and innovation.
          </p>
        </div>

        <div className="lg:max-w-6xl mx-auto">
          <div className="mb-10 lg:mb-12 relative map-fade-container rounded-xl overflow-hidden">
            <MapContainer
              key={`${mapState.center}-${mapState.zoom}`}
              center={mapState.center}
              zoom={mapState.zoom}
              className="w-full h-[300px] sm:h-[400px] md:h-[450px]"
              zoomControl={false}
              scrollWheelZoom={false}
              dragging={!isMobile}
              touchZoom={!isMobile}
              doubleClickZoom={false}
              attributionControl={false}
            >
              <TileLayer
                url="https://{s}.basemaps.cartocdn.com/rastertiles/voyager/{z}/{x}/{y}{r}.png"
                attribution='© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors © <a href="https://carto.com/attributions">CARTO</a>'
              />
              {locations.map((loc) => (
                <Marker
                  key={`overview-${loc.id}`}
                  position={loc.coords}
                  icon={activeTab === loc.id ? activeIcon : defaultIcon}
                >
                  <Popup className="custom-popup">
                    <div className="p-1">
                      <h4 className="font-semibold text-white text-center">
                        {loc.title}
                      </h4>
                    </div>
                  </Popup>
                </Marker>
              ))}
            </MapContainer>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-12 gap-6 lg:gap-8">
          <div className="lg:col-span-8">
            <div className="card-makonis-glass p-4 lg:p-6 h-full flex flex-col">
              <div className="flex-grow">
                <OfficeImageCarousel />
              </div>
            </div>
          </div>
          <div className="lg:col-span-4 space-y-6 lg:space-y-8">
            <div className="card-makonis-glass p-4 lg:p-5">
              <h3 className="text-lg font-semibold text-white mb-4">
                Select Office
              </h3>
              <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-2 gap-2">
                {locations.map((location) => (
                  <button
                    key={location.id}
                    className={`px-3 py-2 rounded-lg font-medium transition-all duration-300 text-sm ${
                      activeTab === location.id
                        ? "bg-makonis-gradient text-white shadow-glow transform scale-105"
                        : "bg-white/10 text-gray-300 hover:bg-white/20 hover:text-white border border-white/20"
                    }`}
                    onClick={() => setActiveTab(location.id)}
                  >
                    {location.title}
                  </button>
                ))}
              </div>
            </div>
            {currentOffice && (
              <div className="card-makonis-glass p-4 lg:p-5 animate-fade-in">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-10 h-10 bg-makonis-gradient rounded-lg flex items-center justify-center flex-shrink-0">
                    <i className="fas fa-building text-white text-base"></i>
                  </div>
                  <h3 className="text-lg font-semibold text-white">
                    {currentOffice.fullTitle}
                  </h3>
                </div>
                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <div className="w-8 h-8 bg-makonis-secondary/20 rounded-md flex items-center justify-center flex-shrink-0 mt-0.5">
                      <i className="fas fa-map-marker-alt text-makonis-secondary text-sm"></i>
                    </div>
                    <div>
                      <span className="text-xs font-medium text-makonis-secondary uppercase tracking-wider">
                        Address
                      </span>
                      <p className="text-gray-300 mt-1 leading-relaxed text-sm">
                        {currentOffice.address}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-8 h-8 bg-makonis-secondary/20 rounded-md flex items-center justify-center flex-shrink-0">
                      <i className="fas fa-phone text-makonis-secondary text-sm"></i>
                    </div>
                    <div>
                      <span className="text-xs font-medium text-makonis-secondary uppercase tracking-wider">
                        Phone
                      </span>
                      <a
                        href={`tel:${currentOffice.phone}`}
                        className="block text-white hover:text-makonis-secondary transition-colors duration-300 mt-1 text-sm"
                      >
                        {currentOffice.phone}
                      </a>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-8 h-8 bg-makonis-secondary/20 rounded-md flex items-center justify-center flex-shrink-0">
                      <i className="fas fa-envelope text-makonis-secondary text-sm"></i>
                    </div>
                    <div>
                      <span className="text-xs font-medium text-makonis-secondary uppercase tracking-wider">
                        Email
                      </span>
                      <a
                        href={`mailto:${currentOffice.email}`}
                        className="block text-white hover:text-makonis-secondary transition-colors duration-300 mt-1 text-sm"
                      >
                        {currentOffice.email}
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContactPage;
